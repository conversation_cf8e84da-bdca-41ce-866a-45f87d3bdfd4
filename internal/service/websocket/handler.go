/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      handler.go
 *
 * DESCRIPTION :    WebSocket server implementation for the VPN client
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package websocket

import (
	"net/http"
	"sync"
	"time"

	"github.com/gorilla/websocket"

	"mobile/internal/common/logger"
	"mobile/internal/connection/manager"
	"mobile/internal/connection/server"
	"mobile/internal/service/api"
)

/*****************************************************************************
 * NAME: Handler
 *
 * DESCRIPTION:
 *     Represents a WebSocket handler for real-time communication with clients
 *
 * FIELDS:
 *     upgrader          - WebSocket connection upgrader
 *     clients           - Map of active WebSocket connections
 *     clientsMutex      - Mutex for thread-safe client map operations
 *     broadcast         - Channel for broadcasting events to all clients
 *     connectionManager - VPN connection manager instance
 *     serverManager     - Server management instance
 *     log               - Logger instance for this handler
 *     heartbeatInterval - Interval for sending heartbeat messages
 *     stopCh            - Channel for stopping background goroutines
 *****************************************************************************/
type Handler struct {
	upgrader          websocket.Upgrader
	clients           map[*websocket.Conn]bool
	clientsMutex      sync.Mutex
	broadcast         chan *api.WebSocketEvent
	connectionManager *manager.Manager
	serverManager     *server.Manager
	log               logger.Logger
	heartbeatInterval time.Duration
	stopCh            chan struct{}
}

/*****************************************************************************
 * NAME: NotifyStatusChange
 *
 * DESCRIPTION:
 *     Implements the manager.StatusNotifier interface to handle status changes
 *     from the connection manager and broadcast them to WebSocket clients
 *
 * PARAMETERS:
 *     status - Connection status information from the manager
 *****************************************************************************/
func (h *Handler) NotifyStatusChange(status *manager.Status) {
	// Calculate connection time (Unix timestamp)
	var connectedTime int64
	if !status.ConnectedTime.IsZero() {
		connectedTime = status.ConnectedTime.Unix()
	}

	// Create API status model
	statusInfo := &api.StatusInfo{
		Status:        status.Status,
		Message:       status.Message,
		ConnectedTime: connectedTime,
	}

	// Add server information if available
	if status.Server != nil {
		statusInfo.Server = &api.ServerInfo{
			ID:         status.Server.ID,
			Name:       status.Server.Name,
			NameEn:     status.Server.NameEn,
			ServerName: status.Server.ServerName,
			ServerPort: status.Server.ServerPort,
			Ping:       status.Server.Ping,
			IsAuto:     status.Server.IsAuto,
			Status:     status.Server.Status,
		}
	}

	// Broadcast status update to all clients
	h.broadcast <- api.NewStatusEvent(statusInfo)
}

/*****************************************************************************
 * NAME: NotifyReconnectRequired
 *
 * DESCRIPTION:
 *     Implements the manager.StatusNotifier interface to handle reconnect
 *     required notifications from the connection manager and broadcast them
 *     to WebSocket clients
 *
 * PARAMETERS:
 *     reason  - Reason for requiring reconnect
 *     message - Detailed message describing the reason
 *****************************************************************************/
func (h *Handler) NotifyReconnectRequired(reason, message string) {
	h.log.Info("Reconnect required notification received",
		logger.String("reason", reason),
		logger.String("message", message))

	// Broadcast reconnect required event to all clients
	h.broadcast <- api.NewReconnectRequiredEvent(reason, message)
}

/*****************************************************************************
 * NAME: HandlerConfig
 *
 * DESCRIPTION:
 *     Configuration structure for the WebSocket handler
 *
 * FIELDS:
 *     HeartbeatInterval - Interval for sending heartbeat messages to clients
 *****************************************************************************/
type HandlerConfig struct {
	HeartbeatInterval time.Duration
}

/*****************************************************************************
 * NAME: DefaultHandlerConfig
 *
 * DESCRIPTION:
 *     Returns the default configuration for the WebSocket handler
 *
 * PARAMETERS:
 *     None
 *
 * RETURNS:
 *     *HandlerConfig - Default configuration with 30-second heartbeat interval
 *****************************************************************************/
func DefaultHandlerConfig() *HandlerConfig {
	return &HandlerConfig{
		HeartbeatInterval: 30 * time.Second,
	}
}

/*****************************************************************************
 * NAME: NewHandler
 *
 * DESCRIPTION:
 *     Creates a new WebSocket handler with the specified configuration and dependencies
 *
 * PARAMETERS:
 *     config            - Handler configuration (uses default if nil)
 *     connectionManager - VPN connection manager instance
 *     serverManager     - Server management instance
 *     log               - Logger instance
 *
 * RETURNS:
 *     *Handler - New WebSocket handler instance
 *****************************************************************************/
func NewHandler(config *HandlerConfig, connectionManager *manager.Manager, serverManager *server.Manager, log logger.Logger) *Handler {
	if config == nil {
		config = DefaultHandlerConfig()
	}

	return &Handler{
		upgrader: websocket.Upgrader{
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
			CheckOrigin: func(r *http.Request) bool {
				return true // Allow all origins
			},
		},
		clients:           make(map[*websocket.Conn]bool),
		broadcast:         make(chan *api.WebSocketEvent, 256),
		connectionManager: connectionManager,
		serverManager:     serverManager,
		log:               log,
		heartbeatInterval: config.HeartbeatInterval,
		stopCh:            make(chan struct{}),
	}
}

/*****************************************************************************
 * NAME: Start
 *
 * DESCRIPTION:
 *     Starts the WebSocket handler and all background goroutines
 *
 * PARAMETERS:
 *     None
 *****************************************************************************/
func (h *Handler) Start() {
	h.log.Info("Starting WebSocket handler")
	go h.broadcastLoop()
	go h.heartbeatLoop()
	go h.serverUpdateLoop()

	// Register as status notifier
	if h.connectionManager != nil {
		h.connectionManager.SetStatusNotifier(h)
		h.log.Info("Registered as status notifier for connection manager")
	}

	h.log.Info("WebSocket handler is ready for real-time updates")
}

/*****************************************************************************
 * NAME: Stop
 *
 * DESCRIPTION:
 *     Stops the WebSocket handler and all background goroutines
 *
 * PARAMETERS:
 *     None
 *****************************************************************************/
func (h *Handler) Stop() {
	close(h.stopCh)
}

/*****************************************************************************
 * NAME: SetConnectionManager
 *
 * DESCRIPTION:
 *     Sets the connection manager for the handler, allowing for delayed
 *     initialization when the connection manager is created after the handler
 *
 * PARAMETERS:
 *     connectionManager - VPN connection manager instance
 *****************************************************************************/
func (h *Handler) SetConnectionManager(connectionManager *manager.Manager) {
	h.connectionManager = connectionManager
	h.log.Info("Connection manager set for WebSocket handler")

	// Re-register as status notifier if connection manager is set
	if h.connectionManager != nil {
		h.connectionManager.SetStatusNotifier(h)
		h.log.Info("Re-registered as status notifier for connection manager")
	}
}

/*****************************************************************************
 * NAME: ServeHTTP
 *
 * DESCRIPTION:
 *     Handles WebSocket connections by upgrading HTTP connections and managing
 *     client lifecycle including registration, initial status sending, and cleanup
 *
 * PARAMETERS:
 *     w - HTTP response writer
 *     r - HTTP request
 *****************************************************************************/
func (h *Handler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	// Log connection request
	h.log.Info("WebSocket connection request received",
		logger.String("remote_addr", r.RemoteAddr),
		logger.String("user_agent", r.UserAgent()))

	// Upgrade the HTTP connection to a WebSocket connection
	conn, err := h.upgrader.Upgrade(w, r, nil)
	if err != nil {
		h.log.Error("Failed to upgrade to WebSocket",
			logger.String("remote_addr", r.RemoteAddr),
			logger.ErrorField(err))
		return
	}

	// Log connection success
	h.log.Info("WebSocket connection established",
		logger.String("remote_addr", r.RemoteAddr),
		logger.String("conn_id", conn.RemoteAddr().String()))

	// Register the client
	//h.clientsMutex.Lock()
	h.clients[conn] = true
	clientCount := len(h.clients)
	//h.clientsMutex.Unlock()

	h.log.Info("WebSocket client registered",
		logger.String("conn_id", conn.RemoteAddr().String()),
		logger.Int("total_clients", clientCount))

	// Send initial status
	if h.connectionManager != nil {
		status := h.connectionManager.GetStatus()
		// Convert to API model
		// Calculate connection time (Unix timestamp)
		var connectedTime int64
		if !status.ConnectedTime.IsZero() {
			connectedTime = status.ConnectedTime.Unix()
		}

		statusInfo := &api.StatusInfo{
			Status:        status.Status,
			Message:       status.Message,
			ConnectedTime: connectedTime,
		}

		if status.Server != nil {
			statusInfo.Server = &api.ServerInfo{
				ID:         status.Server.ID,
				Name:       status.Server.Name,
				NameEn:     status.Server.NameEn,
				ServerName: status.Server.ServerName,
				ServerPort: status.Server.ServerPort,
				Ping:       status.Server.Ping,
				IsAuto:     status.Server.IsAuto,
				Status:     status.Server.Status,
			}
		}

		h.log.Info("Sending initial status to new client",
			logger.String("conn_id", conn.RemoteAddr().String()),
			logger.String("status", status.Status),
			logger.String("message", status.Message))

		h.sendToClient(conn, api.NewStatusEvent(statusInfo))
	}

	// Handle client disconnection
	defer func() {
		//h.clientsMutex.Lock()
		delete(h.clients, conn)
		clientCount := len(h.clients)
		//h.clientsMutex.Unlock()
		conn.Close()

		h.log.Info("WebSocket client disconnected",
			logger.String("conn_id", conn.RemoteAddr().String()),
			logger.Int("remaining_clients", clientCount))
	}()

	// Read messages from the client (we don't expect any, but we need to keep the connection alive)
	for {
		_, _, err := conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				h.log.Error("WebSocket read error",
					logger.String("conn_id", conn.RemoteAddr().String()),
					logger.ErrorField(err))
			} else {
				h.log.Info("WebSocket connection closed",
					logger.String("conn_id", conn.RemoteAddr().String()),
					logger.String("reason", err.Error()))
			}
			break
		}
	}
}

/*****************************************************************************
 * NAME: Broadcast
 *
 * DESCRIPTION:
 *     Sends an event to all connected WebSocket clients
 *
 * PARAMETERS:
 *     event - WebSocket event to broadcast
 *****************************************************************************/
func (h *Handler) Broadcast(event *api.WebSocketEvent) {
	h.log.Debug("Broadcasting event to clients",
		logger.String("event_type", event.Event))
	h.broadcast <- event
}

/*****************************************************************************
 * NAME: broadcastLoop
 *
 * DESCRIPTION:
 *     Background goroutine that broadcasts events to all connected clients
 *
 * PARAMETERS:
 *     None
 *****************************************************************************/
func (h *Handler) broadcastLoop() {
	for {
		select {
		case event := <-h.broadcast:
			//h.clientsMutex.Lock()
			clientCount := len(h.clients)
			if clientCount > 0 {
				h.log.Debug("Broadcasting event to clients",
					logger.String("event_type", event.Event),
					logger.Int("client_count", clientCount))

				for client := range h.clients {
					h.sendToClientLocked(client, event)
				}
			} else {
				h.log.Debug("No clients to broadcast event",
					logger.String("event_type", event.Event))
			}
			//h.clientsMutex.Unlock()
		case <-h.stopCh:
			return
		}
	}
}

/*****************************************************************************
 * NAME: heartbeatLoop
 *
 * DESCRIPTION:
 *     Background goroutine that sends heartbeat events to all connected clients
 *     at regular intervals to keep connections alive
 *
 * PARAMETERS:
 *     None
 *****************************************************************************/
func (h *Handler) heartbeatLoop() {
	ticker := time.NewTicker(h.heartbeatInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			h.Broadcast(api.NewHeartbeatEvent())
		case <-h.stopCh:
			return
		}
	}
}

/*****************************************************************************
 * NAME: serverUpdateLoop
 *
 * DESCRIPTION:
 *     Background goroutine that periodically fetches server list updates,
 *     broadcasts connection server info, interface info, and traffic statistics
 *
 * PARAMETERS:
 *     None
 *****************************************************************************/
func (h *Handler) serverUpdateLoop() {
	if h.serverManager == nil {
		return
	}

	// Create timer to fetch server list every 30 seconds
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	// Immediately fetch server list once
	h.fetchAndBroadcastServers()

	// Register as ping results callback
	h.serverManager.RegisterPingResultsCallback(h.handlePingResults)

	// Register as server list update callback
	h.serverManager.RegisterUpdateCallback(func(servers []server.Server) {
		// Convert to API model
		serverInfos := make([]*api.ServerInfo, 0, len(servers))
		for _, server := range servers {
			serverInfo := &api.ServerInfo{
				ID:         server.ID,
				Name:       server.Name,
				NameEn:     server.NameEn,
				ServerName: server.ServerName,
				ServerPort: server.ServerPort,
				Ping:       server.Ping,
				IsAuto:     server.IsAuto,
				Status:     server.Status,
			}
			serverInfos = append(serverInfos, serverInfo)
		}

		// Broadcast server list update
		h.broadcast <- api.NewServersEvent(serverInfos)

		// Broadcast ping complete event
		h.broadcast <- api.NewWebSocketEvent(api.EventPingComplete, nil)
	})

	// Create timer to broadcast current connection server every 5 seconds
	connServerTicker := time.NewTicker(5 * time.Second)
	defer connServerTicker.Stop()

	// Create timer to broadcast traffic statistics every 1 second
	trafficTicker := time.NewTicker(1 * time.Second)
	defer trafficTicker.Stop()

	// Create timer to clean up dead connections every 30 seconds
	cleanupTicker := time.NewTicker(30 * time.Second)
	defer cleanupTicker.Stop()

	h.log.Info("WebSocket handler timers started",
		logger.String("server_update_interval", "30s"),
		logger.String("connection_server_interval", "5s"),
		logger.String("traffic_stats_interval", "1s"),
		logger.String("cleanup_interval", "30s"))

	for {
		select {
		case <-ticker.C:
			// Fetch and broadcast server list
			h.fetchAndBroadcastServers()
		case <-connServerTicker.C:
			// Broadcast current connection server (only if clients are connected)
			if h.hasActiveClients() {
				h.broadcastCurrentConnectionServer()
			}
		case <-trafficTicker.C:
			// Broadcast traffic statistics (only if clients are connected)
			if h.hasActiveClients() {
				h.broadcastTrafficStats()
			}
		case <-cleanupTicker.C:
			// Clean up dead connections to prevent memory leaks
			h.cleanupDeadConnections()
		case <-h.stopCh:
			h.log.Info("WebSocket handler stopping, cleaning up timers")
			return
		}
	}
}

/*****************************************************************************
 * NAME: fetchAndBroadcastServers
 *
 * DESCRIPTION:
 *     Fetches the current server list and broadcasts it to all connected clients
 *
 * PARAMETERS:
 *     None
 *****************************************************************************/
func (h *Handler) fetchAndBroadcastServers() {
	if h.serverManager == nil {
		return
	}

	// Get server list
	servers := h.serverManager.GetServers()

	// Get default server
	defaultServer, _ := h.serverManager.GetDefaultServer()

	// Convert to API model
	serverInfos := make([]*api.ServerInfo, 0, len(servers))
	for _, server := range servers {
		// Create server info
		serverInfo := &api.ServerInfo{
			ID:         server.ID,
			Name:       server.Name,
			NameEn:     server.NameEn,
			ServerName: server.ServerName,
			ServerPort: server.ServerPort,
			Ping:       server.Ping,
			IsAuto:     server.IsAuto,
			Status:     server.Status,
		}

		// Set whether it's the default server
		if defaultServer != nil && server.ID == defaultServer.ID {
			serverInfo.IsDefault = true
		}

		serverInfos = append(serverInfos, serverInfo)
	}

	// Broadcast server list update
	h.broadcast <- api.NewServersEvent(serverInfos)
}

/*****************************************************************************
 * NAME: sendToClient
 *
 * DESCRIPTION:
 *     Sends an event to a specific client with proper mutex locking
 *
 * PARAMETERS:
 *     client - WebSocket connection to send to
 *     event  - Event to send
 *****************************************************************************/
func (h *Handler) sendToClient(client *websocket.Conn, event *api.WebSocketEvent) {
	//h.clientsMutex.Lock()
	//defer h.clientsMutex.Unlock()
	h.sendToClientLocked(client, event)
}

/*****************************************************************************
 * NAME: sendToClientLocked
 *
 * DESCRIPTION:
 *     Sends an event to a specific client without acquiring mutex lock
 *     (assumes caller already holds the lock)
 *
 * PARAMETERS:
 *     client - WebSocket connection to send to
 *     event  - Event to send
 *****************************************************************************/
func (h *Handler) sendToClientLocked(client *websocket.Conn, event *api.WebSocketEvent) {
	// Skip detailed logging for heartbeat events to avoid log spam
	if event.Event != api.EventHeartbeat {
		h.log.Debug("Sending event to client",
			logger.String("conn_id", client.RemoteAddr().String()),
			logger.String("event_type", event.Event))
	}

	if err := client.WriteJSON(event); err != nil {
		h.log.Error("Failed to send WebSocket event",
			logger.String("conn_id", client.RemoteAddr().String()),
			logger.String("event_type", event.Event),
			logger.ErrorField(err))
		client.Close()
		delete(h.clients, client)
	} else if event.Event != api.EventHeartbeat {
		// Skip detailed logging for heartbeat events to avoid log spam
		h.log.Debug("Successfully sent event to client",
			logger.String("conn_id", client.RemoteAddr().String()),
			logger.String("event_type", event.Event))
	}
}

/*****************************************************************************
 * NAME: handlePingResults
 *
 * DESCRIPTION:
 *     Handles ping results callback from server manager and broadcasts
 *     the updated server list with ping results to all clients
 *
 * PARAMETERS:
 *     servers - List of servers with updated ping results
 *****************************************************************************/
func (h *Handler) handlePingResults(servers []server.Server) {
	// Convert to API model
	serverInfos := make([]*api.ServerInfo, 0, len(servers))
	for _, server := range servers {
		serverInfo := &api.ServerInfo{
			ID:         server.ID,
			Name:       server.Name,
			NameEn:     server.NameEn,
			ServerName: server.ServerName,
			ServerPort: server.ServerPort,
			Ping:       server.Ping,
			IsAuto:     server.IsAuto,
			Status:     server.Status,
		}
		serverInfos = append(serverInfos, serverInfo)
	}

	// Broadcast ping results
	h.broadcast <- api.NewPingResultsEvent(serverInfos)
}

/*****************************************************************************
 * NAME: broadcastCurrentConnectionServer
 *
 * DESCRIPTION:
 *     Broadcasts information about the currently connected server to all clients
 *
 * PARAMETERS:
 *     None
 *****************************************************************************/
func (h *Handler) broadcastCurrentConnectionServer() {
	if h.serverManager == nil {
		return
	}

	// Get current connection server
	currentServer := h.serverManager.GetCurrentConnectionServer()
	if currentServer == nil {
		return
	}

	// Convert to API model
	serverInfo := &api.ServerInfo{
		ID:         currentServer.ID,
		Name:       currentServer.Name,
		NameEn:     currentServer.NameEn,
		ServerName: currentServer.ServerName,
		ServerPort: currentServer.ServerPort,
		Ping:       currentServer.Ping,
		IsAuto:     currentServer.IsAuto,
		Status:     currentServer.Status,
	}

	// Broadcast current connection server
	h.broadcast <- api.NewConnServerEvent(serverInfo)
}

/*****************************************************************************
 * NAME: broadcastTrafficStats
 *
 * DESCRIPTION:
 *     Broadcasts traffic statistics to all connected clients, including
 *     upload/download speeds and total bytes transferred
 *
 * PARAMETERS:
 *     None
 *****************************************************************************/
func (h *Handler) broadcastTrafficStats() {
	if h.connectionManager == nil {
		h.log.Debug("Connection manager not available for traffic stats broadcast")
		return
	}

	// Update traffic speed statistics
	h.connectionManager.UpdateTrafficSpeed()

	// Get traffic statistics
	stats := h.connectionManager.GetTrafficStats()

	// Log debug info only when there's actual traffic to avoid log spam
	if stats.TotalUpload > 0 || stats.TotalDownload > 0 || stats.UploadSpeed > 0 || stats.DownloadSpeed > 0 {
		h.log.Debug("Broadcasting traffic stats",
			logger.Int64("upload_speed", stats.UploadSpeed),
			logger.Int64("download_speed", stats.DownloadSpeed),
			logger.Int64("total_upload", stats.TotalUpload),
			logger.Int64("total_download", stats.TotalDownload))
		// Broadcast traffic statistics
		h.broadcast <- api.NewTrafficEvent(
			stats.UploadSpeed,
			stats.DownloadSpeed,
			stats.TotalUpload,
			stats.TotalDownload,
		)
	}
}

/*****************************************************************************
 * NAME: hasActiveClients
 *
 * DESCRIPTION:
 *     Checks if there are any active WebSocket clients connected
 *
 * RETURNS:
 *     bool - True if there are active clients, false otherwise
 *****************************************************************************/
func (h *Handler) hasActiveClients() bool {
	h.clientsMutex.Lock()
	defer h.clientsMutex.Unlock()
	return len(h.clients) > 0
}

/*****************************************************************************
 * NAME: cleanupDeadConnections
 *
 * DESCRIPTION:
 *     Cleans up dead WebSocket connections to prevent memory leaks.
 *     Sends ping messages to all clients and removes unresponsive ones.
 *
 * PARAMETERS:
 *     None
 *****************************************************************************/
func (h *Handler) cleanupDeadConnections() {
	h.clientsMutex.Lock()
	defer h.clientsMutex.Unlock()

	if len(h.clients) == 0 {
		return
	}

	h.log.Debug("Starting WebSocket connection cleanup",
		logger.Int("total_clients", len(h.clients)))

	deadConnections := make([]*websocket.Conn, 0)

	// Send ping to all clients and identify dead connections
	for client := range h.clients {
		if err := client.WriteMessage(websocket.PingMessage, []byte{}); err != nil {
			h.log.Debug("Client failed ping test, marking for removal",
				logger.String("conn_id", client.RemoteAddr().String()),
				logger.ErrorField(err))
			deadConnections = append(deadConnections, client)
		}
	}

	// Remove dead connections
	for _, deadConn := range deadConnections {
		delete(h.clients, deadConn)
		deadConn.Close()
		h.log.Info("Removed dead WebSocket connection",
			logger.String("conn_id", deadConn.RemoteAddr().String()))
	}

	if len(deadConnections) > 0 {
		h.log.Info("WebSocket connection cleanup completed",
			logger.Int("removed_connections", len(deadConnections)),
			logger.Int("remaining_clients", len(h.clients)))
	}
}
