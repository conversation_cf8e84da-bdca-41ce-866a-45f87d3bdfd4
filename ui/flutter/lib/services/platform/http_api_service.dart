// /**
//  * AUTHOR: wei
//  * HISTORY: 27/06/2025 - Initial implementation of Windows/Linux HTTP API service
//  */

import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:web_socket_channel/web_socket_channel.dart';
import 'cross_platform_api_service.dart';
import 'cross_platform_log_service.dart';
import 'platform_service_factory.dart';
import '../../models/connection_status.dart';
import '../../models/server.dart';
import '../../models/user_info.dart';
import '../../models/routing_settings.dart';
import '../../models/interface_info.dart';
import '../../utils/api_exception.dart';
import '../../utils/constants.dart';
import '../../utils/memory_monitor.dart';

/// HttpApiService
///
/// PURPOSE:
///     Windows/Linux implementation of CrossPlatformApiService using HTTP API.
///     Communicates with Go backend through HTTP requests and WebSocket events.
///     Maintains compatibility with existing backend service architecture.
///
/// FEATURES:
///     - HTTP API calls for all VPN operations
///     - WebSocket connection for real-time events
///     - Backend service health monitoring
///     - Timeout and retry handling
///     - Legacy compatibility with existing code
///
/// USAGE:
///     Created by PlatformServiceFactory for Windows/Linux platforms.
///     Wraps existing ApiService functionality in new interface.
class HttpApiService implements CrossPlatformApiService {
  
  // ============================================================================
  // CONFIGURATION
  // ============================================================================
  
  final String baseUrl;
  final String webSocketUrl;
  
  // Timeout constants
  static const Duration _defaultTimeout = Duration(seconds: 10);
  static const Duration _pingTimeout = Duration(seconds: 20);
  static const Duration _connectTimeout = Duration(seconds: 25); // 与iOS和Android后端保持一致
  static const Duration _healthCheckTimeout = Duration(seconds: 20);
  
  // ============================================================================
  // STATE MANAGEMENT
  // ============================================================================
  
  bool _isInitialized = false;
  StreamController<Map<String, dynamic>>? _eventController;
  WebSocketChannel? _webSocketChannel;
  StreamSubscription? _webSocketSubscription;
  Timer? _reconnectTimer;
  Timer? _heartbeatTimer;
  bool _isWebSocketConnected = false;

  // Reconnection management to prevent infinite reconnect loops
  int _reconnectAttempts = 0;
  static const int _maxReconnectAttempts = 10;
  static const Duration _maxReconnectInterval = Duration(minutes: 5);

  // Store original login response for best server extraction
  Map<String, dynamic>? _lastLoginResponse;

  // Lazy-initialized log service for Windows debug logging
  CrossPlatformLogService? _logService;

  // ============================================================================
  // CONSTRUCTOR
  // ============================================================================
  
  HttpApiService({
    this.baseUrl = 'http://localhost:56543/api',
    this.webSocketUrl = 'ws://localhost:56544/ws',
  });

  // ============================================================================
  // DEBUG LOGGING HELPERS
  // ============================================================================

  /// _initializeLogService
  ///
  /// DESCRIPTION:
  ///     Initialize log service for Windows debug logging
  ///
  /// RETURNS:
  ///     void
  void _initializeLogService() {
    if (_logService == null && defaultTargetPlatform == TargetPlatform.windows) {
      try {
        _logService = PlatformServiceFactory.createLogService();
        _logService!.initialize().catchError((e) {
          // Ignore initialization errors, fallback to debugPrint
        });
      } catch (e) {
        // Ignore creation errors, fallback to debugPrint
      }
    }
  }

  /// _logDebug
  ///
  /// DESCRIPTION:
  ///     Log debug message to Windows UI log system
  ///
  /// PARAMETERS:
  ///     message - Debug message to log
  ///
  /// RETURNS:
  ///     void
  void _logDebug(String message) {
    if (defaultTargetPlatform == TargetPlatform.windows) {
      _initializeLogService();

      if (_logService != null) {
        try {
          _logService!.debug('HttpApiService', '🔍 [MEMORY_DEBUG] $message');
        } catch (e) {
          // Fallback to debugPrint
          debugPrint('🔍 [MEMORY_DEBUG] HttpApiService: $message');
        }
      } else {
        // Fallback to debugPrint
        debugPrint('🔍 [MEMORY_DEBUG] HttpApiService: $message');
      }
    }
  }
  
  // ============================================================================
  // INITIALIZATION AND LIFECYCLE
  // ============================================================================
  
  @override
  Future<bool> initialize() async {
    if (_isInitialized) {
      return true;
    }

    try {
      // Start memory monitoring for Windows platform
      if (defaultTargetPlatform == TargetPlatform.windows) {
        MemoryMonitor.instance.startMonitoring();
        _logDebug('HttpApiService.initialize() - Memory monitoring started');
      }

      // Initialize event stream controller
      _eventController = StreamController<Map<String, dynamic>>.broadcast();

      // Check if backend service is available
      final isHealthy = await healthCheck();
      if (!isHealthy) {
        throw Exception('Backend service not available');
      }

      // Setup WebSocket connection for events
      await _setupWebSocketConnection();

      _isInitialized = true;

      if (defaultTargetPlatform == TargetPlatform.windows) {
        _logDebug('HttpApiService.initialize() - Completed successfully');
      }

      return true;
    } catch (e) {
      throw ApiException(
        'Failed to initialize HTTP API service: $e',
        1000,
        'initialization_failed'
      );
    }
  }
  
  @override
  Future<void> dispose() async {
    if (defaultTargetPlatform == TargetPlatform.windows) {
      _logDebug('HttpApiService.dispose() - Starting cleanup');
    }

    // Cancel timers with memory monitoring
    if (_reconnectTimer != null) {
      _reconnectTimer?.cancel();
      if (defaultTargetPlatform == TargetPlatform.windows) {
        MemoryMonitor.instance.logTimerDestroyed('http_api_reconnect_timer');
      }
    }

    if (_heartbeatTimer != null) {
      _heartbeatTimer?.cancel();
      if (defaultTargetPlatform == TargetPlatform.windows) {
        MemoryMonitor.instance.logTimerDestroyed('http_api_heartbeat_timer');
      }
    }

    // Close WebSocket connection with monitoring
    if (_webSocketChannel != null) {
      await _webSocketSubscription?.cancel();
      await _webSocketChannel?.sink.close();
      if (defaultTargetPlatform == TargetPlatform.windows) {
        MemoryMonitor.instance.logWebSocketDisconnected();
      }
    }

    await _eventController?.close();

    _eventController = null;
    _webSocketChannel = null;
    _webSocketSubscription = null;
    _reconnectTimer = null;
    _heartbeatTimer = null;
    _isWebSocketConnected = false;
    _isInitialized = false;

    // Stop memory monitoring
    if (defaultTargetPlatform == TargetPlatform.windows) {
      MemoryMonitor.instance.stopMonitoring();
      _logDebug('HttpApiService.dispose() - Cleanup completed');
    }
  }
  
  /// _setupWebSocketConnection
  ///
  /// DESCRIPTION:
  ///     Setup WebSocket connection for real-time events.
  ///     Ensures proper cleanup of existing connections before creating new ones.
  ///
  /// RETURNS:
  ///     Future<void> - completion of WebSocket setup
  Future<void> _setupWebSocketConnection() async {
    try {
      if (defaultTargetPlatform == TargetPlatform.windows) {
        debugPrint('🔍 [MEMORY_DEBUG] HttpApiService._setupWebSocketConnection() - Starting WebSocket setup');
      }

      // Clean up any existing WebSocket resources before creating new connection
      // This is crucial to prevent resource leaks during reconnections
      _cleanupWebSocketResources();

      final uri = Uri.parse(webSocketUrl);
      _webSocketChannel = WebSocketChannel.connect(uri);

      _webSocketSubscription = _webSocketChannel!.stream.listen(
        (message) {
          _handleWebSocketMessage(message);
        },
        onError: (error) {
          _handleWebSocketError(error);
        },
        onDone: () {
          _handleWebSocketDone();
        },
        cancelOnError: false,
      );

      _isWebSocketConnected = true;

      // Log WebSocket connection with memory monitoring
      if (defaultTargetPlatform == TargetPlatform.windows) {
        MemoryMonitor.instance.logWebSocketConnected();
        debugPrint('🔍 [MEMORY_DEBUG] HttpApiService._setupWebSocketConnection() - WebSocket connected successfully');
      }

      _startHeartbeat();
    } catch (e) {
      // WebSocket connection is optional, don't fail initialization
      debugPrint('WebSocket connection failed: $e');
      if (defaultTargetPlatform == TargetPlatform.windows) {
        debugPrint('🔍 [MEMORY_DEBUG] HttpApiService._setupWebSocketConnection() - WebSocket connection failed: $e');
      }

      // Ensure connection state is properly reset on failure
      _isWebSocketConnected = false;
    }
  }
  
  /// _handleWebSocketMessage
  ///
  /// DESCRIPTION:
  ///     Handle incoming WebSocket messages and convert to events.
  ///     Ensures compatibility with mobile_old WebSocket message format.
  ///
  /// PARAMETERS:
  ///     message - raw WebSocket message
  ///
  /// RETURNS:
  ///     void
  void _handleWebSocketMessage(dynamic message) {
    try {
      final data = json.decode(message as String);

      // 确保消息格式与mobile_old版本一致
      if (data is Map<String, dynamic> && data.containsKey('event')) {
        final eventType = data['event'] as String? ?? 'unknown';

        // Log event processing for memory monitoring
        if (defaultTargetPlatform == TargetPlatform.windows) {
          MemoryMonitor.instance.logEventProcessed('websocket_$eventType');
        }

        // 消息格式正确，直接转发
        _eventController?.add(data);
      } else if (data is Map<String, dynamic>) {
        // 如果消息没有event字段，记录警告但仍然转发
        debugPrint('WebSocket message missing event field: $data');

        if (defaultTargetPlatform == TargetPlatform.windows) {
          MemoryMonitor.instance.logEventProcessed('websocket_no_event_field');
        }

        _eventController?.add(data);
      }
    } catch (e) {
      debugPrint('Failed to parse WebSocket message: $e');

      if (defaultTargetPlatform == TargetPlatform.windows) {
        MemoryMonitor.instance.logEventProcessed('websocket_parse_error');
      }
    }
  }
  
  /// _handleWebSocketError
  ///
  /// DESCRIPTION:
  ///     Handle WebSocket connection errors.
  ///
  /// PARAMETERS:
  ///     error - WebSocket error
  ///
  /// RETURNS:
  ///     void
  void _handleWebSocketError(dynamic error) {
    if (defaultTargetPlatform == TargetPlatform.windows) {
      debugPrint('🔍 [MEMORY_DEBUG] HttpApiService._handleWebSocketError() - WebSocket error: $error');
    }

    _isWebSocketConnected = false;

    // Clean up existing resources before reconnecting
    _cleanupWebSocketResources();

    _eventController?.addError(error);

    // Only schedule reconnect if service is still initialized
    if (_isInitialized) {
      _scheduleReconnect();
    }
  }

  /// _handleWebSocketDone
  ///
  /// DESCRIPTION:
  ///     Handle WebSocket connection closure.
  ///
  /// RETURNS:
  ///     void
  void _handleWebSocketDone() {
    if (defaultTargetPlatform == TargetPlatform.windows) {
      debugPrint('🔍 [MEMORY_DEBUG] HttpApiService._handleWebSocketDone() - WebSocket connection closed');
    }

    _isWebSocketConnected = false;

    // Clean up existing resources before reconnecting
    _cleanupWebSocketResources();

    // Only schedule reconnect if service is still initialized
    if (_isInitialized) {
      _scheduleReconnect();
    }
  }

  /// _cleanupWebSocketResources
  ///
  /// DESCRIPTION:
  ///     Clean up WebSocket-related resources to prevent memory leaks.
  ///
  /// RETURNS:
  ///     void
  void _cleanupWebSocketResources() {
    // Stop heartbeat timer to prevent it from running on a dead connection
    if (_heartbeatTimer != null) {
      _heartbeatTimer?.cancel();
      if (defaultTargetPlatform == TargetPlatform.windows) {
        MemoryMonitor.instance.logTimerDestroyed('http_api_heartbeat_timer');
        debugPrint('🔍 [MEMORY_DEBUG] HttpApiService._cleanupWebSocketResources() - Heartbeat timer cleaned up');
      }
      _heartbeatTimer = null;
    }

    // Cancel WebSocket subscription
    if (_webSocketSubscription != null) {
      _webSocketSubscription?.cancel();
      _webSocketSubscription = null;

      if (defaultTargetPlatform == TargetPlatform.windows) {
        debugPrint('🔍 [MEMORY_DEBUG] HttpApiService._cleanupWebSocketResources() - WebSocket subscription cancelled');
      }
    }

    // Close WebSocket channel
    if (_webSocketChannel != null) {
      _webSocketChannel?.sink.close();
      _webSocketChannel = null;

      if (defaultTargetPlatform == TargetPlatform.windows) {
        MemoryMonitor.instance.logWebSocketDisconnected();
        debugPrint('🔍 [MEMORY_DEBUG] HttpApiService._cleanupWebSocketResources() - WebSocket channel closed');
      }
    }
  }
  
  /// _scheduleReconnect
  ///
  /// DESCRIPTION:
  ///     Schedule WebSocket reconnection attempt.
  ///     Ensures proper cleanup of existing timers to prevent memory leaks.
  ///
  /// RETURNS:
  ///     void
  void _scheduleReconnect() {
    // Cancel existing timer with monitoring
    if (_reconnectTimer != null) {
      _reconnectTimer?.cancel();
      if (defaultTargetPlatform == TargetPlatform.windows) {
        MemoryMonitor.instance.logTimerDestroyed('http_api_reconnect_timer');
      }
      _reconnectTimer = null; // Ensure null assignment to prevent reference leaks
    }

    // Only schedule reconnect if service is still initialized
    if (!_isInitialized) {
      if (defaultTargetPlatform == TargetPlatform.windows) {
        debugPrint('🔍 [MEMORY_DEBUG] HttpApiService._scheduleReconnect() - Service not initialized, skipping reconnect');
      }
      return;
    }

    // Create new timer with monitoring
    _reconnectTimer = Timer(const Duration(seconds: 3), () {
      // Double-check state before attempting reconnection
      if (_isInitialized && !_isWebSocketConnected) {
        if (defaultTargetPlatform == TargetPlatform.windows) {
          debugPrint('🔍 [MEMORY_DEBUG] HttpApiService._scheduleReconnect() - Attempting WebSocket reconnection');
        }
        _setupWebSocketConnection();
      } else {
        if (defaultTargetPlatform == TargetPlatform.windows) {
          debugPrint('🔍 [MEMORY_DEBUG] HttpApiService._scheduleReconnect() - Skipping reconnect (initialized: $_isInitialized, connected: $_isWebSocketConnected)');
        }
      }

      // Clear timer reference after execution
      _reconnectTimer = null;
      if (defaultTargetPlatform == TargetPlatform.windows) {
        MemoryMonitor.instance.logTimerDestroyed('http_api_reconnect_timer');
      }
    });

    if (defaultTargetPlatform == TargetPlatform.windows) {
      MemoryMonitor.instance.logTimerCreated('http_api_reconnect_timer');
      debugPrint('🔍 [MEMORY_DEBUG] HttpApiService._scheduleReconnect() - Reconnect timer scheduled');
    }
  }
  
  /// _startHeartbeat
  ///
  /// DESCRIPTION:
  ///     Start WebSocket heartbeat to keep connection alive.
  ///     Ensures proper cleanup of existing timers to prevent memory leaks.
  ///
  /// RETURNS:
  ///     void
  void _startHeartbeat() {
    // Cancel existing heartbeat timer with monitoring
    if (_heartbeatTimer != null) {
      _heartbeatTimer?.cancel();
      if (defaultTargetPlatform == TargetPlatform.windows) {
        MemoryMonitor.instance.logTimerDestroyed('http_api_heartbeat_timer');
      }
      _heartbeatTimer = null; // Ensure null assignment to prevent reference leaks
    }

    // Only start heartbeat if service is initialized and WebSocket is connected
    if (!_isInitialized || !_isWebSocketConnected) {
      if (defaultTargetPlatform == TargetPlatform.windows) {
        debugPrint('🔍 [MEMORY_DEBUG] HttpApiService._startHeartbeat() - Skipping heartbeat start (initialized: $_isInitialized, connected: $_isWebSocketConnected)');
      }
      return;
    }

    // Create new heartbeat timer with monitoring
    _heartbeatTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      // Double-check state in timer callback to prevent unnecessary operations
      if (!_isInitialized) {
        // Service was disposed, cancel timer
        timer.cancel();
        _heartbeatTimer = null;
        if (defaultTargetPlatform == TargetPlatform.windows) {
          MemoryMonitor.instance.logTimerDestroyed('http_api_heartbeat_timer');
          debugPrint('🔍 [MEMORY_DEBUG] HttpApiService._startHeartbeat() - Timer cancelled due to service disposal');
        }
        return;
      }

      if (_isWebSocketConnected && _webSocketChannel != null) {
        try {
          _webSocketChannel!.sink.add(json.encode({'type': 'ping'}));

          if (defaultTargetPlatform == TargetPlatform.windows) {
            MemoryMonitor.instance.logEventProcessed('websocket_heartbeat_sent');
          }
        } catch (e) {
          // Heartbeat failed, connection likely broken
          _isWebSocketConnected = false;

          if (defaultTargetPlatform == TargetPlatform.windows) {
            debugPrint('🔍 [MEMORY_DEBUG] HttpApiService._startHeartbeat() - Heartbeat failed, scheduling reconnect: $e');
            MemoryMonitor.instance.logEventProcessed('websocket_heartbeat_failed');
          }

          // Cancel current heartbeat timer before scheduling reconnect
          timer.cancel();
          _heartbeatTimer = null;
          if (defaultTargetPlatform == TargetPlatform.windows) {
            MemoryMonitor.instance.logTimerDestroyed('http_api_heartbeat_timer');
          }

          _scheduleReconnect();
        }
      } else {
        // Connection lost, cancel heartbeat timer
        timer.cancel();
        _heartbeatTimer = null;
        if (defaultTargetPlatform == TargetPlatform.windows) {
          MemoryMonitor.instance.logTimerDestroyed('http_api_heartbeat_timer');
          debugPrint('🔍 [MEMORY_DEBUG] HttpApiService._startHeartbeat() - Timer cancelled due to connection loss');
        }
      }
    });

    if (defaultTargetPlatform == TargetPlatform.windows) {
      MemoryMonitor.instance.logTimerCreated('http_api_heartbeat_timer');
      debugPrint('🔍 [MEMORY_DEBUG] HttpApiService._startHeartbeat() - Heartbeat timer started (30s interval)');
    }
  }
  
  // ============================================================================
  // HTTP REQUEST HELPERS
  // ============================================================================
  
  /// _createHeaders
  ///
  /// DESCRIPTION:
  ///     Create standard HTTP headers for API requests.
  ///
  /// RETURNS:
  ///     Map<String, String> - HTTP headers
  Map<String, String> _createHeaders() {
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
  }
  
  /// _handleResponse
  ///
  /// DESCRIPTION:
  ///     Handle HTTP response and throw appropriate exceptions.
  ///
  /// PARAMETERS:
  ///     response - HTTP response object
  ///
  /// RETURNS:
  ///     Map<String, dynamic> - parsed response data
  ///
  /// THROWS:
  ///     ApiException - for HTTP errors or invalid responses
  Map<String, dynamic> _handleResponse(http.Response response) {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      try {
        return json.decode(response.body);
      } catch (e) {
        throw ApiException('Invalid JSON response', response.statusCode, 'invalid_json');
      }
    } else {
      String errorMessage = 'HTTP ${response.statusCode}';
      String errorType = 'http_error';
      int errorCode = response.statusCode;

      try {
        final errorData = json.decode(response.body);
        errorMessage = errorData['message'] ?? errorMessage;
        errorType = errorData['type'] ?? errorType;
        // If backend provides error code, use it instead of HTTP status code
        if (errorData['code'] != null) {
          errorCode = errorData['code'] as int;
        }
      } catch (e) {
        // Use default error message if response body is not JSON
      }

      // Map HTTP status codes to appropriate error codes for better user experience
      if (errorCode == response.statusCode) {
        switch (response.statusCode) {
          case 401:
            // HTTP 401 Unauthorized -> Authentication failed (202)
            errorCode = 202;
            errorType = 'auth_failed';
            break;
          case 403:
            // HTTP 403 Forbidden -> Permission denied
            errorCode = 203;
            errorType = 'permission_denied';
            break;
          case 404:
            // HTTP 404 Not Found -> Resource not found
            errorCode = 7003;
            errorType = 'not_found';
            break;
          case 408:
            // HTTP 408 Request Timeout -> Network timeout
            errorCode = 1001;
            errorType = 'timeout';
            break;
          case 429:
            // HTTP 429 Too Many Requests -> Rate limited
            errorCode = 7008;
            errorType = 'rate_limited';
            break;
          case 500:
            // HTTP 500 Internal Server Error -> Server error
            errorCode = 7002;
            errorType = 'server_error';
            break;
          case 502:
            // HTTP 502 Bad Gateway -> Gateway error
            errorCode = 7009;
            errorType = 'gateway_error';
            break;
          case 503:
            // HTTP 503 Service Unavailable -> Service unavailable
            errorCode = 7010;
            errorType = 'service_unavailable';
            break;
          case 504:
            // HTTP 504 Gateway Timeout -> Gateway timeout
            errorCode = 7011;
            errorType = 'gateway_timeout';
            break;
          default:
            // Keep original HTTP status code for other cases
            break;
        }
      }

      throw ApiException(errorMessage, errorCode, errorType);
    }
  }
  
  /// _ensureInitialized
  ///
  /// DESCRIPTION:
  ///     Ensure service is initialized before API calls.
  ///
  /// THROWS:
  ///     ApiException - if service is not initialized
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw ApiException(
        'HTTP API service not initialized',
        1000,
        'not_initialized'
      );
    }
  }

  /// _parseConnectionStatus
  ///
  /// DESCRIPTION:
  ///     Parse connection status from API response data.
  ///
  /// PARAMETERS:
  ///     data - response data containing status information
  ///
  /// RETURNS:
  ///     ConnectionStatus - parsed connection status
  ConnectionStatus _parseConnectionStatus(Map<String, dynamic> data) {
    final statusString = data['status']?.toString().toLowerCase();

    switch (statusString) {
      case 'connected':
        return ConnectionStatus.connected;
      case 'connecting':
        return ConnectionStatus.connecting;
      case 'disconnecting':
        return ConnectionStatus.disconnecting;
      case 'error':
        return ConnectionStatus.error;
      default:
        return ConnectionStatus.disconnected;
    }
  }

  // ============================================================================
  // AUTHENTICATION
  // ============================================================================

  @override
  Future<UserInfo> login(String username, String password) async {
    _ensureInitialized();

    try {
      final response = await http.post(
        Uri.parse('$baseUrl/login'),
        headers: _createHeaders(),
        body: json.encode({
          'username': username,
          'password': password,
        }),
      ).timeout(_defaultTimeout);

      final responseData = _handleResponse(response);

      // Save original login response for best server extraction
      _lastLoginResponse = responseData;

      // Extract the actual user data from the API response wrapper
      final data = responseData['data'] as Map<String, dynamic>?;
      if (data == null) {
        throw ApiException('Invalid login response: missing data field', 1001, 'invalid_response');
      }

      return UserInfo.fromJson(data);
    } on TimeoutException {
      throw ApiException('Login request timeout', 1001, 'timeout');
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException('Login failed: ${e.toString()}', 1001, 'unknown');
    }
  }

  @override
  Future<void> logout() async {
    _ensureInitialized();

    // Clear stored login response on logout
    _lastLoginResponse = null;

    // 注意：后端不提供logout API端点，logout操作只需要清除本地数据
    // 这个方法保持为空操作，实际的logout逻辑在AuthService中处理
    // 保持接口一致性，避免抛出异常
  }

  /// getLastLoginResponse
  ///
  /// DESCRIPTION:
  ///     获取最后一次登录的原始响应数据，用于提取best_server信息
  ///
  /// RETURNS:
  ///     Map<String, dynamic>? - 原始登录响应数据，如果没有则返回null
  Map<String, dynamic>? getLastLoginResponse() {
    return _lastLoginResponse;
  }

  // ============================================================================
  // SERVER MANAGEMENT
  // ============================================================================

  @override
  Future<List<Server>> getServers() async {
    _ensureInitialized();

    try {
      final response = await http.get(
        Uri.parse('$baseUrl/servers'),
        headers: _createHeaders(),
      ).timeout(_defaultTimeout);

      final responseData = _handleResponse(response);

      // Extract the actual server list from the API response wrapper
      final data = responseData['data'] as List<dynamic>?;
      if (data == null) {
        throw ApiException('Invalid servers response: missing data field', 1003, 'invalid_response');
      }

      return _parseServerListResponse({'data': data});
    } on TimeoutException {
      throw ApiException('Get servers request timeout', 1003, 'timeout');
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException('Get servers failed: ${e.toString()}', 1003, 'unknown');
    }
  }

  /// _parseServerListResponse
  ///
  /// DESCRIPTION:
  ///     解析服务器列表响应数据，处理不同的响应格式
  ///
  /// PARAMETERS:
  ///     data - 响应数据
  ///
  /// RETURNS:
  ///     List<Server> - 解析后的服务器列表
  List<Server> _parseServerListResponse(dynamic data) {
    // 处理不同的响应格式
    if (data is List) {
      // 直接返回了服务器列表
      return data.map((server) => Server.fromJson(server)).toList();
    } else if (data is Map) {
      // 检查各种可能的嵌套结构
      if (data['servers'] != null && data['servers'] is List) {
        return (data['servers'] as List).map((server) => Server.fromJson(server)).toList();
      } else if (data['serverlist'] != null && data['serverlist'] is List) {
        return (data['serverlist'] as List).map((server) => Server.fromJson(server)).toList();
      } else if (data['data'] != null) {
        if (data['data'] is List) {
          return (data['data'] as List).map((server) => Server.fromJson(server)).toList();
        } else if (data['data'] is Map) {
          if (data['data']['servers'] != null && data['data']['servers'] is List) {
            return (data['data']['servers'] as List).map((server) => Server.fromJson(server)).toList();
          } else if (data['data']['serverlist'] != null && data['data']['serverlist'] is List) {
            return (data['data']['serverlist'] as List).map((server) => Server.fromJson(server)).toList();
          }
        }
      }
    }

    // 如果无法解析，返回空列表
    return [];
  }

  @override
  Future<int> pingServer(String serverId) async {
    _ensureInitialized();

    try {
      final response = await http.post(
        Uri.parse('$baseUrl/servers/ping'),
        headers: _createHeaders(),
        body: json.encode({
          'server_id': serverId,
        }),
      ).timeout(_pingTimeout);

      final responseData = _handleResponse(response);

      // Extract the actual ping data from the API response wrapper
      final data = responseData['data'] as Map<String, dynamic>?;
      if (data == null) {
        return -1; // Return -1 for invalid response instead of throwing
      }

      return data['latency'] as int? ?? -1;
    } catch (e) {
      // Return -1 for ping failures instead of throwing
      return -1;
    }
  }

  @override
  Future<void> pingServers() async {
    _ensureInitialized();

    try {
      final response = await http.post(
        Uri.parse('$baseUrl/servers/ping'),
        headers: _createHeaders(),
        body: json.encode({}), // Empty body for ping all servers
      ).timeout(_pingTimeout);

      _handleResponse(response);
    } on TimeoutException {
      throw ApiException('Ping servers request timeout', 1005, 'timeout');
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException('Ping servers failed: ${e.toString()}', 1005, 'unknown');
    }
  }

  // ============================================================================
  // VPN CONNECTION CONTROL
  // ============================================================================

  @override
  Future<Map<String, dynamic>?> connect(String serverId, String username, String password) async {
    _ensureInitialized();

    try {
      final response = await http.post(
        Uri.parse('$baseUrl/connect'),
        headers: _createHeaders(),
        body: json.encode({
          'server_id': serverId,
          'username': username,
          'password': password, // Windows平台传递密文密码
        }),
      ).timeout(_connectTimeout);

      final responseData = _handleResponse(response);

      // Extract connect response data which includes interface info
      final data = responseData['data'] as Map<String, dynamic>?;
      if (data != null) {
        debugPrint('HttpApiService: Connect response data: $data');
        return data;
      }

      return null;
    } on TimeoutException {
      throw ApiException('Connection request timeout', 1004, 'timeout');
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException('Connection failed: ${e.toString()}', 1004, 'unknown');
    }
  }

  @override
  Future<void> disconnect() async {
    _ensureInitialized();

    try {
      final response = await http.post(
        Uri.parse('$baseUrl/disconnect'),
        headers: _createHeaders(),
      ).timeout(_defaultTimeout);

      _handleResponse(response);
    } on TimeoutException {
      throw ApiException('Disconnect request timeout', 1005, 'timeout');
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException('Disconnect failed: ${e.toString()}', 1005, 'unknown');
    }
  }

  @override
  Future<void> reconnect() async {
    _ensureInitialized();

    // 注意：后端不提供reconnect API端点，通过disconnect + connect实现重连
    try {
      // 先断开连接
      await disconnect();

      // 等待一小段时间确保断开完成
      await Future.delayed(const Duration(milliseconds: 500));

      // 重新连接需要服务器ID，但这里没有参数
      // 这个方法在当前架构下可能需要重新设计
      // 暂时抛出未实现异常，提示调用方使用disconnect + connect
      throw ApiException('Reconnect not supported - use disconnect() then connect(serverId)', 1006, 'not_supported');
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException('Reconnect failed: ${e.toString()}', 1006, 'unknown');
    }
  }

  // ============================================================================
  // STATUS AND MONITORING
  // ============================================================================

  @override
  Future<ConnectionStatus> getConnectionStatus() async {
    _ensureInitialized();

    try {
      final response = await http.get(
        Uri.parse('$baseUrl/status'),
        headers: _createHeaders(),
      ).timeout(_defaultTimeout);

      final responseData = _handleResponse(response);

      // Extract the actual status data from the API response wrapper
      final data = responseData['data'] as Map<String, dynamic>?;
      if (data == null) {
        return ConnectionStatus.disconnected; // Return disconnected for invalid response
      }

      return _parseConnectionStatus(data);
    } catch (e) {
      // Return disconnected status for errors instead of throwing
      return ConnectionStatus.disconnected;
    }
  }

  @override
  Future<InterfaceInfo> getInterfaceInfo() async {
    _ensureInitialized();

    try {
      // Debug: Log HTTP API call
      debugPrint('HttpApiService: Calling GET $baseUrl/interface');

      final response = await http.get(
        Uri.parse('$baseUrl/interface'),
        headers: _createHeaders(),
      ).timeout(_defaultTimeout);

      // Debug: Log HTTP response
      debugPrint('HttpApiService: Response status: ${response.statusCode}');
      debugPrint('HttpApiService: Response body: ${response.body}');

      final responseData = _handleResponse(response);

      // Debug: Log parsed response
      debugPrint('HttpApiService: Parsed response: $responseData');

      // Extract the actual interface data from the API response wrapper
      final data = responseData['data'] as Map<String, dynamic>?;
      if (data == null) {
        throw ApiException('Invalid interface info response: missing data field', 1007, 'invalid_response');
      }

      // Debug: Log interface data
      debugPrint('HttpApiService: Interface data: $data');

      final interfaceInfo = InterfaceInfo.fromJson(data);

      // Debug: Log created InterfaceInfo
      debugPrint('HttpApiService: Created InterfaceInfo - '
          'interfaceName: ${interfaceInfo.interfaceName}, '
          'localIp: ${interfaceInfo.localIp}, '
          'tunIp: ${interfaceInfo.tunIp}');

      return interfaceInfo;
    } on TimeoutException {
      debugPrint('HttpApiService: getInterfaceInfo timeout');
      throw ApiException('Get interface info timeout', 1007, 'timeout');
    } catch (e) {
      debugPrint('HttpApiService: getInterfaceInfo error: $e');
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException('Get interface info failed: ${e.toString()}', 1007, 'unknown');
    }
  }

  @override
  Future<bool> healthCheck() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/health'),
        headers: _createHeaders(),
      ).timeout(_healthCheckTimeout);

      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  // ============================================================================
  // ROUTING CONFIGURATION
  // ============================================================================

  @override
  Future<RoutingSettingsModel> getRoutingSettings() async {
    _ensureInitialized();

    try {
      final response = await http.get(
        Uri.parse('$baseUrl/settings/routing'),
        headers: _createHeaders(),
      ).timeout(_defaultTimeout);

      final responseData = _handleResponse(response);

      // Extract the actual routing settings from the API response wrapper
      final data = responseData['data'] as Map<String, dynamic>?;
      if (data == null) {
        throw ApiException('Invalid routing settings response: missing data field', 1008, 'invalid_response');
      }

      final settings = RoutingSettingsModel();
      settings.fromJson(data);
      return settings;
    } on TimeoutException {
      throw ApiException('Get routing settings timeout', 1008, 'timeout');
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException('Get routing settings failed: ${e.toString()}', 1008, 'unknown');
    }
  }

  @override
  Future<void> updateRoutingSettings(RoutingSettingsModel settings) async {
    _ensureInitialized();

    try {
      // 只发送路由相关的设置给后端，不包含auto_start
      // 保持与mobile_old版本的兼容性
      final routingOnlyData = {
        'mode': settings.mode == RoutingMode.custom ? 'custom' : 'all',
        'custom_routes': settings.customRoutes,
      };

      final response = await http.post(
        Uri.parse('$baseUrl/settings/routing'),
        headers: _createHeaders(),
        body: json.encode(routingOnlyData),
      ).timeout(_defaultTimeout);

      _handleResponse(response);
    } on TimeoutException {
      throw ApiException('Update routing settings timeout', 1009, 'timeout');
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException('Update routing settings failed: ${e.toString()}', 1009, 'unknown');
    }
  }

  @override
  Future<void> setServerProviderUrl(String url) async {
    _ensureInitialized();

    try {
      final response = await http.post(
        Uri.parse('$baseUrl/servers/provider'),
        headers: _createHeaders(),
        body: json.encode({
          'url': url,
        }),
      ).timeout(_defaultTimeout);

      _handleResponse(response);
    } on TimeoutException {
      throw ApiException('Set server provider URL timeout', 1010, 'timeout');
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException('Set server provider URL failed: ${e.toString()}', 1010, 'unknown');
    }
  }

  // ============================================================================
  // EVENT STREAMING
  // ============================================================================

  @override
  Stream<Map<String, dynamic>> get eventStream {
    return _eventController?.stream ?? const Stream.empty();
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  @override
  Future<void> shutdown() async {
    try {
      await http.post(
        Uri.parse('$baseUrl/shutdown'),
        headers: _createHeaders(),
      ).timeout(const Duration(seconds: 5));
    } catch (e) {
      // Ignore shutdown errors
    }

    await dispose();
  }
}
