/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      websocket_service.dart
///
/// DESCRIPTION :    WebSocket通信服务，负责与后端的实时双向通信，包括连接管理、
///                  事件处理、心跳维护、自动重连和消息分发等核心功能
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create

import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/status.dart' as status;

import '../models/server.dart';
import '../models/routing_settings.dart';
import '../utils/constants.dart';
import '../utils/memory_monitor.dart';
import '../core/dependency_injection.dart';
import '../core/app_state.dart';
import 'api_service.dart';
import 'reconnect_service.dart';
import 'notification_service.dart';
import 'language_service.dart';
import 'platform/platform_service_factory.dart';
import 'platform/cross_platform_api_service.dart';

/// WebSocketService
///
/// PURPOSE:
///     WebSocket通信服务，负责与后端的实时双向通信和事件处理
///
/// FEATURES:
///     - 连接管理：自动连接、断开、重连机制
///     - 事件处理：消息解析、事件分发、通知处理
///     - 心跳维护：定期心跳保持连接活跃
///     - 错误恢复：智能重连策略和错误处理
///     - 状态管理：连接状态跟踪和通知
///     - 本地化支持：多语言通知消息
///     - 日志记录：详细的调试和错误日志
///
/// USAGE:
///     通过依赖注入获取实例，监听events流处理WebSocket事件，
///     调用connect/disconnect管理连接状态
class WebSocketService {
  final String url;
  final ApiService apiService;
  final NotificationService notificationService;
  WebSocketChannel? _channel;
  Timer? _reconnectTimer;
  Timer? _heartbeatTimer;
  bool _isConnected = false;
  bool _isConnecting = false;
  final Duration _reconnectDelay = const Duration(seconds: 3);
  final Duration _heartbeatInterval = const Duration(seconds: 30);
  int _reconnectAttempts = 0;
  final int _maxReconnectAttempts = 10;

  // 事件流控制器
  final StreamController<Map<String, dynamic>> _eventController = StreamController<Map<String, dynamic>>.broadcast();

  // 平台服务支持
  CrossPlatformApiService? _platformApiService;
  StreamSubscription? _platformEventSubscription;
  bool _usePlatformService = false;

  // 状态变化检测 - 用于避免重复通知，初始化为disconnect状态
  ConnectionStatus? _lastNotifiedStatus = ConnectionStatus.disconnected;

  /// events getter
  ///
  /// DESCRIPTION:
  ///     获取WebSocket事件流，用于监听各种WebSocket事件
  ///
  /// RETURNS:
  ///     Stream<Map<String, dynamic>> - WebSocket事件流
  Stream<Map<String, dynamic>> get events => _eventController.stream;

  /// WebSocketService构造函数
  ///
  /// DESCRIPTION:
  ///     创建WebSocket服务实例，自动检测平台并选择合适的事件源
  ///
  /// PARAMETERS:
  ///     url - WebSocket服务器URL（仅用于Windows/Linux平台）
  ///     apiService - API服务实例
  ///     notificationService - 通知服务实例
  WebSocketService({
    required this.url,
    required this.apiService,
    required this.notificationService,
  }) {
    _initializePlatformService();
  }

  /// _initializePlatformService
  ///
  /// DESCRIPTION:
  ///     初始化平台服务，检测当前平台并设置相应的事件源
  ///
  /// RETURNS:
  ///     void
  void _initializePlatformService() {
    try {
      // 所有平台都使用CrossPlatformApiService的事件流
      // iOS/macOS使用Platform Channel，Windows/Linux使用HTTP+WebSocket
      _usePlatformService = true;
      _platformApiService = PlatformServiceFactory.createApiService();
      _setupPlatformEventListening();
    } catch (e) {
      // 平台服务初始化失败，回退到直接WebSocket模式
      _usePlatformService = false;
      apiService.logService.warning('WebSocket', 'Platform service initialization failed, falling back to direct WebSocket: $e');
    }
  }

  /// _setupPlatformEventListening
  ///
  /// DESCRIPTION:
  ///     设置平台事件监听，将CrossPlatformApiService事件转发到WebSocket事件流
  ///     并进行与mobile_old版本一致的事件处理
  ///
  /// RETURNS:
  ///     void
  void _setupPlatformEventListening() {
    if (_platformApiService == null) return;

    _platformEventSubscription = _platformApiService!.eventStream.listen(
      (event) {
        // apiService.logService.debug('WebSocket', 'Received event from PlatformApiService: $event');
        // 对事件进行与mobile_old版本一致的处理
        _processPlatformEvent(event);
      },
      onError: (error) {
        // apiService.logService.debug('WebSocket', 'Platform event stream error: $error');
        apiService.logService.error('WebSocket', 'Platform event stream error: $error');
        _eventController.addError(error);
      },
    );
  }

  /// _processPlatformEvent
  ///
  /// DESCRIPTION:
  ///     处理来自CrossPlatformApiService的事件，确保与mobile_old版本的处理逻辑一致
  ///
  /// PARAMETERS:
  ///     event - 平台事件数据
  ///
  /// RETURNS:
  ///     void
  void _processPlatformEvent(Map<String, dynamic> event) {
    try {
      // apiService.logService.debug('WebSocket', 'Processing platform event: $event');
      // 检查事件格式是否符合预期
      if (event.containsKey('event')) {
        final eventType = event['event'] as String;
        final eventData = event['data'];
        // apiService.logService.debug('WebSocket', 'Event type: $eventType, data: $eventData');

        // 处理心跳响应
        if (eventType == eventHeartbeat) {
          return;
        }

        // 处理通知类事件
        _processNotificationEvent(eventType, eventData);
      }

      // 统一在这里转发事件到事件流（避免重复添加）
      // apiService.logService.debug('WebSocket', 'Adding event to _eventController: $event');
      _eventController.add(event);
    } catch (e) {
      // apiService.logService.debug('WebSocket', 'Platform event processing error: $e');
      apiService.logService.error('WebSocket', 'Platform event processing error: $e');
      // 处理出错时仍然转发原始事件，但只添加一次
      _eventController.add(event);
    }
  }

  /// connect
  ///
  /// DESCRIPTION:
  ///     连接到事件源，根据平台选择WebSocket或Platform Channel
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<bool> - 连接是否成功建立
  Future<bool> connect() async {
    if (_isConnected || _isConnecting) {
      return _isConnected;
    }

    _isConnecting = true;

    try {
      // iOS/macOS平台使用Platform Channel事件流
      if (_usePlatformService) {
        return await _connectPlatformService();
      }

      // Windows/Linux平台使用WebSocket
      return await _connectWebSocket();
    } catch (e) {
      _isConnecting = false;
      _onError(e);
      return false;
    }
  }

  /// _connectPlatformService
  ///
  /// DESCRIPTION:
  ///     连接到Platform Channel事件服务
  ///
  /// RETURNS:
  ///     Future<bool> - 连接是否成功建立
  Future<bool> _connectPlatformService() async {
    try {
      _platformApiService ??= PlatformServiceFactory.createApiService();

      final success = await _platformApiService!.initialize();
      if (success) {
        _setupPlatformEventListening();
        _isConnected = true;
        _isConnecting = false;
        _reconnectAttempts = 0;

        // 通知连接成功
        final timestamp = DateTime.now().millisecondsSinceEpoch ~/ 1000;
        _eventController.add({
          'event': 'websocket_connected',
          'data': {'timestamp': timestamp},
        });

        return true;
      } else {
        _isConnecting = false;
        return false;
      }
    } catch (e) {
      _isConnecting = false;
      apiService.logService.error('WebSocket', 'Platform service connection failed: $e');
      return false;
    }
  }

  /// _connectWebSocket
  ///
  /// DESCRIPTION:
  ///     连接到WebSocket服务器（Windows/Linux平台）
  ///
  /// RETURNS:
  ///     Future<bool> - 连接是否成功建立
  Future<bool> _connectWebSocket() async {
    try {
      // Windows平台内存监控
      if (Platform.isWindows) {
        debugPrint('🔍 [MEMORY_DEBUG] WebSocketService._connectWebSocket() - Starting WebSocket connection');
      }

      // 先检查API服务是否可用
      try {
        final isHealthy = await apiService.healthCheck();
        if (!isHealthy) {
          _isConnecting = false;
          return false;
        }
      } catch (e) {
        // 健康检查异常时继续尝试连接
      }

      // 清理现有连接资源，防止内存泄漏
      _cleanupWebSocketResources();

      final uri = Uri.parse(url);
      _channel = WebSocketChannel.connect(uri);

      _channel!.stream.listen(
        (message) {
          try {
            _onMessage(message);
          } catch (e) {
            // 消息处理错误时记录日志
            apiService.logService.error('WebSocket', 'Message processing error: $e');
          }
        },
        onError: (error) {
          _onError(error);
        },
        onDone: () {
          _onDone();
        },
        cancelOnError: false,
      );

      _isConnected = true;
      _isConnecting = false;
      _reconnectAttempts = 0;

      // Windows平台内存监控
      if (Platform.isWindows) {
        MemoryMonitor.instance.logWebSocketConnected();
        debugPrint('🔍 [MEMORY_DEBUG] WebSocketService._connectWebSocket() - WebSocket connected successfully');
      }

      // 启动心跳定时器
      _startHeartbeat();

      // 通知连接成功
      final timestamp = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      _eventController.add({
        'event': 'websocket_connected',
        'data': {'timestamp': timestamp},
      });

      // 发送一个ping请求，测试服务器响应
      Future.delayed(const Duration(seconds: 1), () {
        try {
          apiService.pingServers();
        } catch (e) {
          apiService.logService.warning('WebSocket', 'Failed to send initial ping: $e');
        }
      });

      return true;
    } catch (e) {
      _isConnecting = false;
      apiService.logService.error('WebSocket', 'WebSocket connection failed: $e');

      if (Platform.isWindows) {
        debugPrint('🔍 [MEMORY_DEBUG] WebSocketService._connectWebSocket() - WebSocket connection failed: $e');
      }

      return false;
    }
  }

  /// disconnect
  ///
  /// DESCRIPTION:
  ///     断开事件源连接，根据平台选择WebSocket或Platform Channel
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void disconnect() {
    if (Platform.isWindows) {
      debugPrint('🔍 [MEMORY_DEBUG] WebSocketService.disconnect() - Starting disconnect');
    }

    _stopHeartbeat();
    _stopReconnectTimer();

    // 断开Platform Channel事件监听
    if (_usePlatformService) {
      _platformEventSubscription?.cancel();
      _platformEventSubscription = null;
    }

    // 断开WebSocket连接
    _cleanupWebSocketResources();

    _isConnected = false;

    // 重置状态跟踪为disconnect状态，确保重新连接时能正常显示通知
    _lastNotifiedStatus = ConnectionStatus.disconnected;

    // 通知断开连接
    _eventController.add({
      'event': 'websocket_disconnected',
      'data': {'timestamp': DateTime.now().millisecondsSinceEpoch ~/ 1000},
    });

    if (Platform.isWindows) {
      debugPrint('🔍 [MEMORY_DEBUG] WebSocketService.disconnect() - Disconnect completed');
    }
  }

  /// _cleanupWebSocketResources
  ///
  /// DESCRIPTION:
  ///     清理WebSocket相关资源，防止内存泄漏
  ///
  /// RETURNS:
  ///     void
  void _cleanupWebSocketResources() {
    if (_channel != null) {
      try {
        _channel!.sink.close(status.normalClosure);
      } catch (e) {
        // Ignore close errors
      }
      _channel = null;

      if (Platform.isWindows) {
        MemoryMonitor.instance.logWebSocketDisconnected();
        debugPrint('🔍 [MEMORY_DEBUG] WebSocketService._cleanupWebSocketResources() - WebSocket channel closed');
      }
    }
  }

  /// _onMessage
  ///
  /// DESCRIPTION:
  ///     处理收到的WebSocket消息，解析JSON并分发事件
  ///
  /// PARAMETERS:
  ///     message - 收到的原始消息
  ///
  /// RETURNS:
  ///     void
  void _onMessage(dynamic message) {
    try {
      final data = json.decode(message as String);

      if (data is Map<String, dynamic> && data.containsKey('event')) {
        final eventType = data['event'] as String;
        final eventData = data['data'];

        // 处理心跳响应
        if (eventType == eventHeartbeat) {
          return;
        }

        // 处理通知类事件
        _processNotificationEvent(eventType, eventData);

        // 转发事件到事件流
        _eventController.add(data);
      }
    } catch (e) {
      apiService.logService.error('WebSocket', 'Message parsing error: $e');
    }
  }

  /// _processNotificationEvent
  ///
  /// DESCRIPTION:
  ///     处理各种类型的WebSocket通知事件，包括状态、错误、通知等
  ///
  /// PARAMETERS:
  ///     eventType - 事件类型
  ///     eventData - 事件数据
  ///
  /// RETURNS:
  ///     void
  void _processNotificationEvent(String eventType, dynamic eventData) {
    final logService = apiService.logService;

    // 记录重要的WebSocket事件到日志（排除高频事件）
    if (eventType != WebSocketEvents.traffic && eventType != WebSocketEvents.heartbeat) {
      // logService.debug('WebSocket', 'Received event: $eventType');
      if (eventData != null && eventType != WebSocketEvents.pingResults) {
        // final dataStr = eventData is String ? eventData : json.encode(eventData);
        // final truncatedData = dataStr.length > 200 ? '${dataStr.substring(0, 200)}...' : dataStr;
        // logService.debug('WebSocket', 'Event data: $truncatedData');
      }
    }

    switch (eventType) {
      case WebSocketEvents.status:
        // logService.debug('WebSocket', 'Processing status event');
        _processStatusEvent(eventData);
        break;
      case WebSocketEvents.error:
        logService.error('WebSocket', 'Processing error event: ${eventData['message'] ?? ''}');
        _processErrorEvent(eventData);
        break;
      case WebSocketEvents.notification:
        _handleNotificationEvent(eventData, logService);
        break;
      case WebSocketEvents.pingStart:
        // logService.debug('WebSocket', 'Starting server latency test');
        break;
      case WebSocketEvents.pingComplete:
        // logService.debug('WebSocket', 'Server latency test completed');
        break;
      case WebSocketEvents.pingResults:
        // logService.debug('WebSocket', 'Received server latency test results');
        _processPingResultsEvent(eventData);
        break;
      case WebSocketEvents.connServer:
        _handleConnServerEvent(eventData, logService);
        break;
      case WebSocketEvents.servers:
        // logService.debug('WebSocket', 'Server list updated');
        break;
      case WebSocketEvents.interfaceInfo:
        // logService.debug('WebSocket', 'Interface info event received: $eventData');
        _processInterfaceInfoEvent(eventData);
        break;
      case WebSocketEvents.traffic:
        _handleTrafficEvent(eventData, logService);
        break;
      case WebSocketEvents.shutdownInitiated:
        logService.info('WebSocket', 'Backend shutdown initiated');
        break;
      case WebSocketEvents.shutdownComplete:
        logService.info('WebSocket', 'Backend shutdown completed');
        break;
      case WebSocketEvents.reconnectRequired:
        logService.info('WebSocket', 'Reconnect required event received');
        _handleReconnectRequiredEvent(eventData, logService);
        break;
      default:
        logService.warning('WebSocket', 'Received unknown event type: $eventType');
        break;
    }
  }

  /// _handleNotificationEvent
  ///
  /// DESCRIPTION:
  ///     处理通知事件，根据级别记录日志并显示通知
  ///
  /// PARAMETERS:
  ///     eventData - 事件数据
  ///     logService - 日志服务实例
  ///
  /// RETURNS:
  ///     void
  void _handleNotificationEvent(dynamic eventData, dynamic logService) {
    final level = eventData['level'] as String? ?? 'info';
    final message = eventData['message'] ?? '';

    // 根据通知级别记录不同级别的日志
    switch (level.toLowerCase()) {
      case 'error':
        logService.error('WebSocket', 'Notification: $message');
        break;
      case 'warning':
        logService.warning('WebSocket', 'Notification: $message');
        break;
      case 'success':
        logService.info('WebSocket', 'Success notification: $message');
        break;
      case 'info':
      default:
        logService.info('WebSocket', 'Notification: $message');
        break;
    }

    _processGeneralNotification(eventData);
  }

  /// _handleConnServerEvent
  ///
  /// DESCRIPTION:
  ///     处理连接服务器事件的日志记录
  ///
  /// PARAMETERS:
  ///     eventData - 事件数据
  ///     logService - 日志服务实例
  ///
  /// RETURNS:
  ///     void
  void _handleConnServerEvent(dynamic eventData, dynamic logService) {
    if (eventData != null && eventData['server'] != null) {
      // final serverName = eventData['server']['name'] ?? 'Unknown';
      // logService.debug('WebSocket', 'Connected to server: $serverName');
    } else {
      // logService.debug('WebSocket', 'Connected to server event');
    }
    _processConnServerEvent(eventData);
  }

  /// _handleTrafficEvent
  ///
  /// DESCRIPTION:
  ///     处理流量统计事件的日志记录
  ///
  /// PARAMETERS:
  ///     eventData - 事件数据
  ///     logService - 日志服务实例
  ///
  /// RETURNS:
  ///     void
  void _handleTrafficEvent(dynamic eventData, dynamic logService) {
    if (eventData != null && eventData is Map<String, dynamic>) {
      // final uploadSpeed = eventData['upload_speed'] ?? 0;
      // final downloadSpeed = eventData['download_speed'] ?? 0;
      // logService.debug('WebSocket', 'Traffic stats updated: upload=${uploadSpeed}B/s, download=${downloadSpeed}B/s');
    }
  }

  /// _handleReconnectRequiredEvent
  ///
  /// DESCRIPTION:
  ///     处理重连需求事件，记录详细日志并触发重连流程
  ///
  /// PARAMETERS:
  ///     eventData - 事件数据，包含reason、message和timestamp
  ///     logService - 日志服务实例
  ///
  /// RETURNS:
  ///     void
  void _handleReconnectRequiredEvent(dynamic eventData, dynamic logService) {
    if (eventData == null || eventData is! Map<String, dynamic>) {
      logService.warning('WebSocket', 'Invalid reconnect required event data');
      return;
    }

    final reason = eventData['reason'] as String? ?? 'unknown';
    final message = eventData['message'] as String? ?? 'No details provided';
    final timestamp = eventData['timestamp'] as int? ?? 0;

    // 转换时间戳为可读格式
    final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    final formattedTime = dateTime.toIso8601String();

    // 记录详细的重连需求日志
    logService.info('WebSocket', '=== RECONNECT REQUIRED EVENT ===');
    logService.info('WebSocket', 'Timestamp: $formattedTime');
    logService.info('WebSocket', 'Reason: $reason');
    logService.info('WebSocket', 'Message: $message');
    logService.info('WebSocket', '================================');

    // 根据不同原因记录特定信息
    switch (reason) {
      case 'network_interface_change':
        logService.warning('WebSocket', 'Network interface configuration changed - connection may be unstable');
        break;
      case 'heartbeat_timeout':
        logService.error('WebSocket', 'Heartbeat timeout detected - server communication lost');
        break;
      case 'connection_lost':
        logService.error('WebSocket', 'Connection lost - network connectivity issues');
        break;
      case 'screen_lock_recovery':
        logService.info('WebSocket', 'Screen lock recovery detected - triggering VPN reconnection to fix network connectivity');
        break;
      default:
        logService.warning('WebSocket', 'Unknown reconnect reason: $reason');
        break;
    }

    // 触发重连流程
    logService.info('WebSocket', 'Triggering UI-controlled reconnection process');
    _triggerReconnectProcess(reason, message, logService);
  }

  /// _triggerReconnectProcess
  ///
  /// DESCRIPTION:
  ///     触发UI控制的重连流程，使用统一的ReconnectService
  ///
  /// PARAMETERS:
  ///     reason - 重连原因
  ///     message - 详细消息
  ///     logService - 日志服务实例
  ///
  /// RETURNS:
  ///     void
  void _triggerReconnectProcess(String reason, String message, dynamic logService) async {
    try {
      logService.info('WebSocket', 'Starting UI-controlled reconnection process');
      logService.info('WebSocket', 'Reason: $reason');
      logService.info('WebSocket', 'Message: $message');

      // 发送重连开始通知到事件流
      _eventController.add({
        'event': 'reconnect_process_started',
        'data': {
          'reason': reason,
          'message': message,
          'timestamp': DateTime.now().millisecondsSinceEpoch ~/ 1000,
        },
      });

      // 执行完整的重连流程：disconnect -> 路由设置 -> connect
      try {
        // 使用ReconnectService执行完整的重连流程：disconnect -> 路由设置 -> connect
        final reconnectService = serviceLocator<ReconnectService>();

        // 执行重连
        final success = await reconnectService.handleReconnectRequired(reason, message);

        if (success) {
          logService.info('WebSocket', 'Reconnection completed successfully');

          // 发送重连成功通知
          _eventController.add({
            'event': 'reconnect_process_completed',
            'data': {
              'reason': reason,
              'message': message,
              'success': true,
              'timestamp': DateTime.now().millisecondsSinceEpoch ~/ 1000,
            },
          });
        } else {
          throw Exception('ReconnectService failed to reconnect');
        }

      } catch (e) {
        logService.error('WebSocket', 'Error during reconnection process: $e');

        // 发送重连失败通知
        _eventController.add({
          'event': 'reconnect_process_failed',
          'data': {
            'reason': reason,
            'message': message,
            'error': e.toString(),
            'timestamp': DateTime.now().millisecondsSinceEpoch ~/ 1000,
          },
        });
      }

    } catch (e) {
      logService.error('WebSocket', 'Error during UI-controlled reconnection process: $e');

      // 发送重连失败通知
      _eventController.add({
        'event': 'reconnect_process_failed',
        'data': {
          'reason': reason,
          'message': message,
          'error': e.toString(),
          'timestamp': DateTime.now().millisecondsSinceEpoch ~/ 1000,
        },
      });
    }
  }

  /// _processPingResultsEvent
  ///
  /// DESCRIPTION:
  ///     处理Ping结果事件，记录服务器延迟信息并找出最佳服务器
  ///
  /// PARAMETERS:
  ///     data - Ping结果数据
  ///
  /// RETURNS:
  ///     void
  void _processPingResultsEvent(dynamic data) {
    if (data == null || data is! Map<String, dynamic>) return;

    if (data['servers'] != null && data['servers'] is List) {
      // final servers = (data['servers'] as List)
      //     .map((server) => Server.fromJson(server))
      //     .toList();

      // final logService = apiService.logService;
      // logService.debug('WebSocket', 'Processing ping results event: ${servers.length} servers');

      // final languageService = serviceLocator<LanguageService>();

      // for (var server in servers) {
      //   if (server.ping > 0) {
      //     final serverDisplayName = server.getDisplayNameByLocale(languageService.isEnglish);
      //     logService.debug('WebSocket', 'Server latency: $serverDisplayName: ${server.ping}ms (${server.status})');
      //   }
      // }

      // 找出延迟最低的服务器
      // final bestServer = _findBestServer(servers);
      // if (bestServer != null) {
      //   logService.debug('WebSocket', 'Best server: ${bestServer.name} (latency: ${bestServer.ping}ms)');
      // }

      // logService.debug('WebSocket', 'Ping results event processing completed, not sending duplicate servers event');
    }
  }



  /// _processConnServerEvent
  ///
  /// DESCRIPTION:
  ///     处理连接服务器事件，记录连接信息并转发到事件流
  ///
  /// PARAMETERS:
  ///     data - 连接服务器事件数据
  ///
  /// RETURNS:
  ///     void
  void _processConnServerEvent(dynamic data) {
    if (data == null || data is! Map<String, dynamic>) return;

    if (data['server'] != null && data['server'] is Map<String, dynamic>) {
      final server = Server.fromJson(data['server']);
      final timestamp = data['timestamp'] as int? ?? 0;

      // final logService = apiService.logService;
      // logService.debug('WebSocket', 'Processing connected server event: server=${server.name}, ping=${server.ping}');

      // 记录连接服务器信息
      // if (server.ping > 0) {
      //   logService.debug('WebSocket', 'Current connected server: ${server.name} (latency: ${server.ping}ms)');
      // } else {
      //   logService.debug('WebSocket', 'Current connected server: ${server.name}');
      // }

      // 将连接服务器事件添加到事件流
      _eventController.add({
        'event': eventConnServer,
        'data': {
          'server': server.toJson(),
          'timestamp': timestamp,
        },
      });
    }
  }

  /// _processStatusEvent
  ///
  /// DESCRIPTION:
  ///     处理状态事件，显示本地化的状态通知
  ///
  /// PARAMETERS:
  ///     data - 状态事件数据
  ///
  /// RETURNS:
  ///     void
  void _processStatusEvent(dynamic data) {
    if (data == null || data is! Map<String, dynamic>) return;

    final status = _parseConnectionStatus(data['status']);
    final message = data['message'] ?? '';

    // 总是处理状态事件，即使消息为空，因为状态变化检测会处理重复通知
    // 如果消息为空，_generateLocalizedStatusMessage会生成默认的本地化消息
    _showLocalizedStatusNotification(status, message);
  }

  /// _processErrorEvent
  ///
  /// DESCRIPTION:
  ///     处理错误事件，显示错误通知
  ///
  /// PARAMETERS:
  ///     data - 错误事件数据
  ///
  /// RETURNS:
  ///     void
  void _processErrorEvent(dynamic data) {
    if (data == null || data is! Map<String, dynamic>) return;

    final message = data['message'] ?? '';
    final code = data['code'] as int? ?? 0;
    final type = data['type'] as String? ?? 'unknown';

    _showErrorNotification(message, code, type);
  }

  /// _processGeneralNotification
  ///
  /// DESCRIPTION:
  ///     处理一般通知事件，根据级别显示相应的通知
  ///
  /// PARAMETERS:
  ///     data - 通知事件数据
  ///
  /// RETURNS:
  ///     void
  void _processGeneralNotification(dynamic data) {
    if (data == null || data is! Map<String, dynamic>) return;

    final message = data['message'] ?? '';
    final level = data['level'] as String? ?? 'info';
    final logService = apiService.logService;

    // 记录通知到日志
    switch (level.toLowerCase()) {
      case 'error':
        logService.error('Notification', message);
        notificationService.showErrorNotification(message, code: 0, type: 'notification');
        break;
      case 'warning':
        logService.warning('Notification', message);
        notificationService.showWarningNotification(message);
        break;
      case 'success':
        // logService.debug('Notification', 'Success: $message');
        notificationService.showSuccessNotification(message);
        break;
      case 'info':
      default:
        // logService.debug('Notification', message);
        notificationService.showInfoNotification(message);
        break;
    }
  }

  /// _onError
  ///
  /// DESCRIPTION:
  ///     处理WebSocket连接错误，通知错误并启动重连
  ///
  /// PARAMETERS:
  ///     error - 错误对象
  ///
  /// RETURNS:
  ///     void
  void _onError(dynamic error) {
    // 通知错误
    _eventController.add({
      'event': 'websocket_error',
      'data': {'error': error.toString()},
    });

    _isConnected = false;

    // 重置状态跟踪为disconnect状态，确保重连后能正常显示通知
    _lastNotifiedStatus = ConnectionStatus.disconnected;

    _startReconnectTimer();
  }

  /// _onDone
  ///
  /// DESCRIPTION:
  ///     处理WebSocket连接关闭，启动重连机制
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void _onDone() {
    _isConnected = false;

    // 重置状态跟踪为disconnect状态，确保重连后能正常显示通知
    _lastNotifiedStatus = ConnectionStatus.disconnected;

    _startReconnectTimer();

    // 通知连接关闭
    _eventController.add({
      'event': 'websocket_closed',
      'data': {'timestamp': DateTime.now().millisecondsSinceEpoch ~/ 1000},
    });
  }

  /// _startReconnectTimer
  ///
  /// DESCRIPTION:
  ///     启动重连定时器，使用指数退避策略
  ///     添加内存监控和更好的资源管理
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void _startReconnectTimer() {
    _stopReconnectTimer();

    if (_reconnectAttempts >= _maxReconnectAttempts) {
      if (Platform.isWindows) {
        debugPrint('🔍 [MEMORY_DEBUG] WebSocketService._startReconnectTimer() - Max reconnect attempts reached, stopping');
      }
      return;
    }

    // 使用指数退避策略，每次重连间隔增加
    final delay = Duration(milliseconds:
      _reconnectDelay.inMilliseconds * (1 << _reconnectAttempts.clamp(0, 6)));

    if (Platform.isWindows) {
      debugPrint('🔍 [MEMORY_DEBUG] WebSocketService._startReconnectTimer() - Scheduling reconnect in ${delay.inSeconds}s (attempt ${_reconnectAttempts + 1})');
    }

    _reconnectTimer = Timer(delay, () {
      // Clear timer reference immediately when callback executes
      _reconnectTimer = null;

      if (Platform.isWindows) {
        MemoryMonitor.instance.logTimerDestroyed('websocket_reconnect_timer');
      }

      if (!_isConnected && !_isConnecting) {
        _reconnectAttempts++;

        if (Platform.isWindows) {
          debugPrint('🔍 [MEMORY_DEBUG] WebSocketService._startReconnectTimer() - Executing reconnect attempt ${_reconnectAttempts}');
        }

        connect();
      } else {
        if (Platform.isWindows) {
          debugPrint('🔍 [MEMORY_DEBUG] WebSocketService._startReconnectTimer() - Skipping reconnect (connected: $_isConnected, connecting: $_isConnecting)');
        }
      }
    });

    if (Platform.isWindows) {
      MemoryMonitor.instance.logTimerCreated('websocket_reconnect_timer');
    }
  }

  /// _stopReconnectTimer
  ///
  /// DESCRIPTION:
  ///     停止重连定时器，确保资源正确释放
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void _stopReconnectTimer() {
    if (_reconnectTimer != null) {
      _reconnectTimer?.cancel();
      _reconnectTimer = null;

      if (Platform.isWindows) {
        MemoryMonitor.instance.logTimerDestroyed('websocket_reconnect_timer');
        debugPrint('🔍 [MEMORY_DEBUG] WebSocketService._stopReconnectTimer() - Reconnect timer stopped');
      }
    }
  }

  /// _startHeartbeat
  ///
  /// DESCRIPTION:
  ///     启动心跳定时器，定期发送心跳消息保持连接活跃
  ///     添加内存监控和更好的资源管理
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void _startHeartbeat() {
    _stopHeartbeat();

    if (Platform.isWindows) {
      debugPrint('🔍 [MEMORY_DEBUG] WebSocketService._startHeartbeat() - Starting heartbeat timer (${_heartbeatInterval.inSeconds}s interval)');
    }

    _heartbeatTimer = Timer.periodic(_heartbeatInterval, (timer) {
      // 检查连接状态，如果连接已断开则停止心跳
      if (!_isConnected || _channel == null) {
        timer.cancel();
        _heartbeatTimer = null;

        if (Platform.isWindows) {
          MemoryMonitor.instance.logTimerDestroyed('websocket_heartbeat_timer');
          debugPrint('🔍 [MEMORY_DEBUG] WebSocketService._startHeartbeat() - Heartbeat timer cancelled due to disconnection');
        }
        return;
      }

      try {
        final heartbeatData = json.encode({
          'event': 'heartbeat',
          'data': {'timestamp': DateTime.now().millisecondsSinceEpoch ~/ 1000},
        });
        _channel!.sink.add(heartbeatData);

        if (Platform.isWindows) {
          MemoryMonitor.instance.logEventProcessed('websocket_heartbeat_sent');
        }
      } catch (e) {
        apiService.logService.warning('WebSocket', 'Failed to send heartbeat: $e');

        if (Platform.isWindows) {
          MemoryMonitor.instance.logEventProcessed('websocket_heartbeat_failed');
          debugPrint('🔍 [MEMORY_DEBUG] WebSocketService._startHeartbeat() - Heartbeat failed: $e');
        }

        // 心跳失败时停止定时器，避免无效的重复尝试
        timer.cancel();
        _heartbeatTimer = null;

        if (Platform.isWindows) {
          MemoryMonitor.instance.logTimerDestroyed('websocket_heartbeat_timer');
        }
      }
    });

    if (Platform.isWindows) {
      MemoryMonitor.instance.logTimerCreated('websocket_heartbeat_timer');
    }
  }

  /// _stopHeartbeat
  ///
  /// DESCRIPTION:
  ///     停止心跳定时器，确保资源正确释放
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void _stopHeartbeat() {
    if (_heartbeatTimer != null) {
      _heartbeatTimer?.cancel();
      _heartbeatTimer = null;

      if (Platform.isWindows) {
        MemoryMonitor.instance.logTimerDestroyed('websocket_heartbeat_timer');
        debugPrint('🔍 [MEMORY_DEBUG] WebSocketService._stopHeartbeat() - Heartbeat timer stopped');
      }
    }
  }

  /// _processInterfaceInfoEvent
  ///
  /// DESCRIPTION:
  ///     处理接口信息事件，记录日志
  ///
  /// PARAMETERS:
  ///     data - 接口信息数据
  ///
  /// RETURNS:
  ///     void
  void _processInterfaceInfoEvent(dynamic data) {
    // final logService = apiService.logService;

    if (data == null) {
      // logService.debug('WebSocket', 'Interface info event data is null');
      return;
    }

    if (data is! Map<String, dynamic>) {
      // logService.debug('WebSocket', 'Interface info event data is not Map<String, dynamic>: ${data.runtimeType}');
      return;
    }

    // logService.debug('WebSocket', 'Processing interface info event data: $data');
    // logService.debug('WebSocket', 'Interface info event processing completed');
  }

  // 处理接口信息更新事件 - 已移除，使用DataManager统一处理

  /// handleRoutingSettingsEvent
  ///
  /// DESCRIPTION:
  ///     处理路由设置更新事件，在连接成功时获取最新路由设置
  ///
  /// PARAMETERS:
  ///     routingSettingsModel - 路由设置模型实例
  ///
  /// RETURNS:
  ///     void
  void handleRoutingSettingsEvent(RoutingSettingsModel routingSettingsModel) {
    events.where((event) => event['event'] == eventStatus).listen((event) {
      final data = event['data'];
      if (data == null || data is! Map<String, dynamic>) return;

      final status = _parseConnectionStatus(data['status']);

      // 当连接状态变为已连接时，获取最新的路由设置
      if (status == ConnectionStatus.connected) {
        apiService.getRoutingSettings().then((settings) {
          routingSettingsModel.fromJson(settings);
        }).catchError((error) {
          apiService.logService.warning('WebSocket', 'Failed to get routing settings: $error');
        });
      }
    });
  }

  /// _logStatusChange
  ///
  /// DESCRIPTION:
  ///     记录状态变更日志，不显示通知
  ///
  /// PARAMETERS:
  ///     status - 连接状态
  ///     message - 状态消息
  ///
  /// RETURNS:
  ///     void
  void _logStatusChange(ConnectionStatus status, String message) {
    final logService = apiService.logService;

    try {
      switch (status) {
        case ConnectionStatus.connected:
          logService.info('Connection', 'Connected: $message');
          break;
        case ConnectionStatus.connecting:
          logService.info('Connection', 'Connecting: $message');
          break;
        case ConnectionStatus.disconnecting:
          logService.info('Connection', 'Disconnecting: $message');
          break;
        case ConnectionStatus.disconnected:
          logService.info('Connection', 'Disconnected: $message');
          break;
        case ConnectionStatus.error:
          logService.error('Connection', 'Connection error: $message');
          break;
      }
    } catch (e) {
      logService.error('WebSocket', 'Error logging status change', e);
    }
  }

  // 显示本地化的状态通知
  void _showLocalizedStatusNotification(ConnectionStatus status, String rawMessage) {
    try {
      // final logService = apiService.logService;

      // 获取语言服务和应用状态
      final languageService = serviceLocator<LanguageService>();
      final appState = serviceLocator<AppState>();

      // 生成本地化消息
      String localizedMessage = _generateLocalizedStatusMessage(status, rawMessage, appState.selectedServer, languageService.isEnglish);

      // 检查状态是否真的发生了变化，避免重复通知
      if (_lastNotifiedStatus == status) {
        // 状态没有变化，跳过通知但仍记录日志
        // logService.debug('WebSocket', 'Skipping duplicate status notification: $status - $localizedMessage');
        return;
      }

      // 检查是否为初始状态推送，与AppState保持一致的逻辑
      if (_isInitialStatePush(status, rawMessage)) {
        // 这是初始状态推送，跳过通知但更新状态跟踪
        _lastNotifiedStatus = status;
        // logService.debug('WebSocket', 'Skipping initial state push notification: $status - $rawMessage');
        return;
      }

      // 更新最后通知的状态
      _lastNotifiedStatus = status;

      // 记录到日志
      _logStatusChange(status, rawMessage);

      // 显示本地化通知
      notificationService.showStatusNotification(status, localizedMessage);

      // logService.debug('WebSocket', 'Displayed localized status notification: $status - $localizedMessage');
    } catch (e) {
      final logService = apiService.logService;
      logService.error('WebSocket', 'Failed to display localized status notification, falling back to original message', e);
      // 回退到日志记录
      _logStatusChange(status, rawMessage);
    }
  }

  // 检查是否为初始状态推送，与AppState保持一致的逻辑
  bool _isInitialStatePush(ConnectionStatus status, String message) {
    // 只阻止从connecting到disconnected的明显初始状态推送
    if (_lastNotifiedStatus == ConnectionStatus.connecting && status == ConnectionStatus.disconnected) {
      // 如果消息是简单的 "disconnected" 且没有其他信息，可能是登录时的初始状态推送
      // 但允许包含错误信息的disconnected状态转换（用于错误处理）
      if ((message == StatusMessages.disconnected || message == StatusMessages.disconnectedCapital) &&
          !message.contains('error') && !message.contains('failed') && !message.contains('timeout')) {
        return true;
      }
    }
    return false;
  }

  // 生成本地化状态消息
  String _generateLocalizedStatusMessage(ConnectionStatus status, String rawMessage, Server? server, bool isEnglish) {
    try {
      // 获取服务器名称
      String serverName = '';
      if (server != null) {
        serverName = server.getDisplayNameByLocale(isEnglish);
      }

      // 根据状态和语言生成本地化消息
      switch (status) {
        case ConnectionStatus.connected:
          if (serverName.isNotEmpty) {
            return isEnglish ? 'Connected to $serverName' : '已连接到 $serverName';
          } else {
            return isEnglish ? 'Connected' : '已连接';
          }
        case ConnectionStatus.connecting:
          if (serverName.isNotEmpty) {
            return isEnglish ? 'Connecting to $serverName...' : '正在连接到 $serverName...';
          } else {
            return isEnglish ? 'Connecting...' : '正在连接...';
          }
        case ConnectionStatus.disconnecting:
          return isEnglish ? 'Disconnecting...' : '正在断开连接...';
        case ConnectionStatus.disconnected:
          return isEnglish ? 'Disconnected' : '已断开连接';
        case ConnectionStatus.error:
          return isEnglish ? 'Connection failed' : '连接失败';
      }
    } catch (e) {
      // 如果本地化失败，返回原始消息
      return rawMessage;
    }
  }



  // 显示错误通知
  void _showErrorNotification(String message, int code, String type) {
    // 使用通知服务显示错误通知
    final logService = apiService.logService;

    try {
      // 记录到日志
      logService.error('WebSocket', 'Error notification [$code] $type: $message');

      // 显示通知
      notificationService.showErrorNotification(message, code: code, type: type);
    } catch (e) {
      logService.error('WebSocket', 'Error displaying error notification', e);
    }
  }

  // 处理流量统计更新事件 - 已移除，使用DataManager统一处理

  /// handleServerListEvent
  ///
  /// DESCRIPTION:
  ///     处理服务器列表更新事件，解析服务器数据并调用回调函数
  ///
  /// PARAMETERS:
  ///     onServerListUpdated - 服务器列表更新回调函数
  ///
  /// RETURNS:
  ///     void
  void handleServerListEvent(Function(List<Server>) onServerListUpdated) {
    events.where((event) => event['event'] == eventServers).listen((event) {
      final data = event['data'];
      if (data == null) return;

      try {
        if (data is List) {
          final serverList = data
              .map((server) => Server.fromJson(server))
              .toList();
          onServerListUpdated(serverList);
        } else if (data is Map<String, dynamic> && data['servers'] != null && data['servers'] is List) {
          final serverList = (data['servers'] as List)
              .map((server) => Server.fromJson(server))
              .toList();
          onServerListUpdated(serverList);
        }
      } catch (e) {
        final logService = apiService.logService;
        logService.error('WebSocket', 'Failed to parse server list', e);
      }
    });
  }

  /// handlePingEvents
  ///
  /// DESCRIPTION:
  ///     处理服务器测试事件，监听ping开始和完成事件
  ///
  /// PARAMETERS:
  ///     onPingStart - ping开始回调函数
  ///     onPingComplete - ping完成回调函数
  ///
  /// RETURNS:
  ///     void
  void handlePingEvents(Function() onPingStart, Function() onPingComplete) {
    events.where((event) => event['event'] == eventPingStart).listen((event) {
      onPingStart();
    });

    events.where((event) => event['event'] == eventPingComplete).listen((event) {
      onPingComplete();
    });
  }

  /// handlePingResultsEvent
  ///
  /// DESCRIPTION:
  ///     处理Ping结果事件，解析服务器延迟数据并调用回调函数
  ///
  /// PARAMETERS:
  ///     onPingResults - ping结果回调函数
  ///
  /// RETURNS:
  ///     void
  void handlePingResultsEvent(Function(List<Server>) onPingResults) {
    // final logService = apiService.logService;
    // logService.debug('WebSocket', 'Setting up ping results event handler');

    events.where((event) => event['event'] == eventPingResults).listen((event) {
      // apiService.logService.debug('WebSocket', 'Received ping results event: ${event.toString()}');

      final data = event['data'];
      if (data == null || data is! Map<String, dynamic>) {
        apiService.logService.warning('WebSocket', 'Ping results event has invalid data: $data');
        return;
      }

      try {
        if (data['servers'] != null && data['servers'] is List) {
          final serverList = (data['servers'] as List)
              .map((server) => Server.fromJson(server))
              .toList();

          // apiService.logService.debug('WebSocket', 'Parsed ${serverList.length} servers from ping results');

          // 调用回调函数
          onPingResults(serverList);
          // apiService.logService.debug('WebSocket', 'Called ping results callback with ${serverList.length} servers');

          // final validPingCount = serverList.where((s) => s.ping > 0).length;
          // if (validPingCount > 0) {
          //   apiService.logService.debug('WebSocket', 'Valid latency servers count: $validPingCount');
          //   for (var server in serverList.where((s) => s.ping > 0)) {
          //     apiService.logService.debug('WebSocket', 'Server ${server.name}: ${server.ping}ms');
          //   }
          // } else {
          //   apiService.logService.warning('WebSocket', 'No servers with valid ping results');
          // }
        } else {
          apiService.logService.warning('WebSocket', 'Ping results event missing servers data');
        }
      } catch (e) {
        apiService.logService.error('WebSocket', 'Failed to parse ping results', e);
      }
    });
  }

  /// handleConnServerEvent
  ///
  /// DESCRIPTION:
  ///     处理连接服务器事件，解析服务器信息并调用回调函数
  ///
  /// PARAMETERS:
  ///     onConnServer - 连接服务器回调函数
  ///
  /// RETURNS:
  ///     void
  void handleConnServerEvent(Function(Server) onConnServer) {
    events.where((event) => event['event'] == eventConnServer).listen((event) {
      final data = event['data'];
      if (data == null || data is! Map<String, dynamic>) return;

      try {
        if (data['server'] != null && data['server'] is Map<String, dynamic>) {
          final server = Server.fromJson(data['server']);
          onConnServer(server);

          // final logService = apiService.logService;
          // logService.debug('WebSocket', 'Received connected server event: ${server.name} (latency: ${server.ping}ms)');
        }
      } catch (e) {
        final logService = apiService.logService;
        logService.error('WebSocket', 'Failed to parse connected server event', e);
      }
    });
  }

  /// _parseConnectionStatus
  ///
  /// DESCRIPTION:
  ///     解析连接状态字符串为ConnectionStatus枚举
  ///
  /// PARAMETERS:
  ///     statusStr - 状态字符串
  ///
  /// RETURNS:
  ///     ConnectionStatus - 解析后的连接状态
  ConnectionStatus _parseConnectionStatus(String? statusStr) {
    switch (statusStr) {
      case statusConnected:
        return ConnectionStatus.connected;
      case statusConnecting:
        return ConnectionStatus.connecting;
      case statusDisconnecting:
        return ConnectionStatus.disconnecting;
      case statusError:
        return ConnectionStatus.error;
      case statusDisconnected:
      default:
        return ConnectionStatus.disconnected;
    }
  }

  // 使用常量文件中的常量
  static const String eventStatus = WebSocketEvents.status;
  static const String eventError = WebSocketEvents.error;
  static const String eventServers = WebSocketEvents.servers;
  static const String eventHeartbeat = WebSocketEvents.heartbeat;
  static const String eventPingStart = WebSocketEvents.pingStart;
  static const String eventPingComplete = WebSocketEvents.pingComplete;
  static const String eventPingResults = WebSocketEvents.pingResults;
  static const String eventConnServer = WebSocketEvents.connServer;
  static const String eventTraffic = WebSocketEvents.traffic;
  static const String eventNotification = WebSocketEvents.notification;
  static const String eventInterfaceInfo = WebSocketEvents.interfaceInfo;
  static const String eventReconnectRequired = WebSocketEvents.reconnectRequired;

  static const String statusDisconnected = ConnectionStatusStrings.disconnected;
  static const String statusConnecting = ConnectionStatusStrings.connecting;
  static const String statusConnected = ConnectionStatusStrings.connected;
  static const String statusDisconnecting = ConnectionStatusStrings.disconnecting;
  static const String statusError = ConnectionStatusStrings.error;

  /// dispose
  ///
  /// DESCRIPTION:
  ///     释放WebSocket服务资源，断开连接并关闭事件流
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void dispose() {
    disconnect();

    // 清理平台服务资源
    if (_platformApiService != null) {
      _platformApiService!.dispose();
      _platformApiService = null;
    }

    _eventController.close();
  }

  /// isConnected getter
  ///
  /// DESCRIPTION:
  ///     获取WebSocket连接状态
  ///
  /// RETURNS:
  ///     bool - 是否已连接
  bool get isConnected => _isConnected;

  /// isConnecting getter
  ///
  /// DESCRIPTION:
  ///     获取WebSocket连接中状态
  ///
  /// RETURNS:
  ///     bool - 是否正在连接
  bool get isConnecting => _isConnecting;

  // 用户登录状态
  bool _isAuthenticated = false;

  /// isAuthenticated getter
  ///
  /// DESCRIPTION:
  ///     获取用户认证状态
  ///
  /// RETURNS:
  ///     bool - 是否已认证
  bool get isAuthenticated => _isAuthenticated;

  /// notifyUserLoggedIn
  ///
  /// DESCRIPTION:
  ///     通知WebSocket服务用户已登录，更新认证状态并发送成功通知
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void notifyUserLoggedIn() {
    final logService = apiService.logService;
    logService.info('WebSocket', 'User logged in, updating authentication status');
    _isAuthenticated = true;

    if (_isConnected) {
      try {
        //sendTestNotification('User login successful', 'success');
        logService.info('WebSocket', 'Login success notification sent');
      } catch (e) {
        logService.error('WebSocket', 'Failed to send login success notification', e);
      }
    }
  }
}
