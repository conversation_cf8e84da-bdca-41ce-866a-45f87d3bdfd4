// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Panabit iWAN';

  @override
  String get login => 'Login';

  @override
  String get username => 'Username';

  @override
  String get password => 'Password';

  @override
  String get domain => 'Domain';

  @override
  String get rememberCredentials => 'Remember credentials';

  @override
  String get loginButton => 'Login';

  @override
  String get loginFailed => 'Login failed';

  @override
  String get loginSuccess => 'Login successful';

  @override
  String get logout => 'Logout';

  @override
  String get connect => 'Connect';

  @override
  String get disconnect => 'Disconnect';

  @override
  String get connecting => 'Connecting...';

  @override
  String get connected => 'Connected';

  @override
  String get disconnected => 'Not Connected';

  @override
  String get disconnecting => 'Disconnecting...';

  @override
  String get reconnecting => 'Reconnecting...';

  @override
  String get connectionFailed => 'Connection failed';

  @override
  String get connectionSuccess => 'Connection successful';

  @override
  String get serverList => 'Server List';

  @override
  String get selectServer => 'Select Server';

  @override
  String get noServersAvailable => 'No servers available';

  @override
  String get refreshServers => 'Refresh Servers';

  @override
  String get settings => 'Settings';

  @override
  String get about => 'About';

  @override
  String get statistics => 'Statistics';

  @override
  String get logs => 'Logs';

  @override
  String get language => 'Language';

  @override
  String get theme => 'Theme';

  @override
  String get autoConnect => 'Auto Connect';

  @override
  String get minimizeToTray => 'Minimize to Tray';

  @override
  String get startWithSystem => 'Start with System';

  @override
  String get connectionTime => 'Connection Time';

  @override
  String get dataTransferred => 'Data Transferred';

  @override
  String get ping => 'Ping';

  @override
  String get latency => 'Latency';

  @override
  String get version => 'Version';

  @override
  String get copyright => 'Copyright';

  @override
  String get termsOfService => 'Terms of Service';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get acceptLicense =>
      'I accept the Terms of Service and Privacy Policy';

  @override
  String get licenseRequired =>
      'You must accept the license agreement to continue';

  @override
  String get error => 'Error';

  @override
  String get warning => 'Warning';

  @override
  String get info => 'Information';

  @override
  String get success => 'Success';

  @override
  String get cancel => 'Cancel';

  @override
  String get ok => 'OK';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get save => 'Save';

  @override
  String get close => 'Close';

  @override
  String get exit => 'Exit';

  @override
  String get minimize => 'Minimize';

  @override
  String get maximize => 'Maximize';

  @override
  String get restore => 'Restore';

  @override
  String get copy => 'Copy';

  @override
  String get paste => 'Paste';

  @override
  String get cut => 'Cut';

  @override
  String get selectAll => 'Select All';

  @override
  String get clear => 'Clear';

  @override
  String get refresh => 'Refresh';

  @override
  String get retry => 'Retry';

  @override
  String get loading => 'Loading...';

  @override
  String get pleaseWait => 'Please wait...';

  @override
  String get networkError => 'Network error';

  @override
  String get serverError => 'Server error';

  @override
  String get connectionTimeout => 'Connection timeout';

  @override
  String get invalidCredentials => 'Invalid credentials';

  @override
  String get sessionExpired => 'Session expired';

  @override
  String get accessDenied => 'Access denied';

  @override
  String get serviceUnavailable => 'Service unavailable';

  @override
  String get mainScreen => 'Main';

  @override
  String get connectionScreen => 'Connection';

  @override
  String get userScreen => 'User';

  @override
  String get settingsScreen => 'Settings';

  @override
  String get aboutScreen => 'About';

  @override
  String get logsScreen => 'Logs';

  @override
  String get statisticsScreen => 'Statistics';

  @override
  String get enterServerDomain => 'Enter Server Domain';

  @override
  String get serverDomain => 'Server Domain';

  @override
  String get serverDomainHint => 'e.g.: vpn.example.com';

  @override
  String get nextStep => 'Next';

  @override
  String get clientDomain => 'Client Domain';

  @override
  String get change => 'Change';

  @override
  String get rememberUsernamePassword => 'Remember username and password';

  @override
  String get pleaseEnterServerDomain => 'Please enter server domain';

  @override
  String get pleaseEnterUsername => 'Please enter username';

  @override
  String get pleaseEnterPassword => 'Please enter password';

  @override
  String get pleaseEnterClientDomain => 'Please enter client domain';

  @override
  String get clientDomainHint => 'e.g.: research.staff.unisase';

  @override
  String get lookupServiceError => 'Failed to query server address';

  @override
  String get lookupServiceTimeout => 'Server address query timeout';

  @override
  String get lookupServiceInvalidResponse => 'Invalid response from server';

  @override
  String get lookupServiceNetworkError =>
      'Network connection failed, please check network settings';

  @override
  String get queryingServerAddress => 'Querying server address...';

  @override
  String get startingBackendService => 'Starting backend service...';

  @override
  String get backendServiceStartFailed =>
      'Failed to start backend service, please check permissions or restart application';

  @override
  String get checkingBackendServiceStatus => 'Starting...';

  @override
  String get backendServiceHealthCheckFailed =>
      'Backend service health check failed, please check if port is occupied or restart application';

  @override
  String get autoStart => 'Auto Start';

  @override
  String get autoStartEnabled => 'Auto start enabled';

  @override
  String get autoStartDisabled => 'Auto start disabled';

  @override
  String get routingSettings => 'Routing Settings';

  @override
  String get applying => 'Applying...';

  @override
  String get applySettings => 'Apply Settings';

  @override
  String get settingsAppliedSuccessfully => 'Settings applied successfully';

  @override
  String get noChangesToApply => 'No changes to apply';

  @override
  String get getRoutingSettingsFailed => 'Failed to get routing settings';

  @override
  String get saveAutoStartSettingFailed => 'Failed to save auto start setting';

  @override
  String get saveRoutingSettingsFailed => 'Failed to save routing settings';

  @override
  String get appSettings => 'App Settings';

  @override
  String get appName => 'Application Name';

  @override
  String get versionNumber => 'Version';

  @override
  String get deviceId => 'Device ID';

  @override
  String get gettingDeviceId => 'Getting...';

  @override
  String get getDeviceIdFailed => 'Failed to get';

  @override
  String get agreementsAndContact => 'Agreements';

  @override
  String get viewTermsOfService => 'View Terms of Service';

  @override
  String get viewPrivacyPolicy => 'View Privacy Policy';

  @override
  String get officialWebsite => 'Official Website';

  @override
  String get technicalSupport => 'Technical Support';

  @override
  String get openLinkFailed => 'Failed to open link';

  @override
  String get clickToView => 'Click to view';

  @override
  String get sendEmailFailed => 'Failed to send email';

  @override
  String get vpnClientFeedback => 'WAN Client Feedback';

  @override
  String get allUrlLaunchMethodsFailed => 'All URL launch methods failed';

  @override
  String get openLinkFailedWithError => 'Failed to open link';

  @override
  String get sendEmailFailedWithError => 'Failed to send email';

  @override
  String get statisticsInfo => 'Statistics';

  @override
  String get status => 'Status';

  @override
  String get interface => 'Interface';

  @override
  String get upload => 'Upload';

  @override
  String get download => 'Download';

  @override
  String get localIp => 'Local IP';

  @override
  String get itforceIp => 'Cloud IP';

  @override
  String get personalInfo => 'Personal Information';

  @override
  String get editPersonalInfo => 'Edit Personal Information';

  @override
  String get name => 'Name';

  @override
  String get pleaseEnterName => 'Please enter name';

  @override
  String get department => 'Department';

  @override
  String get position => 'Position';

  @override
  String get accountInfo => 'Account Information';

  @override
  String get clientDomainLabel => 'Client Domain';

  @override
  String get usernameLabel => 'Username';

  @override
  String get deviceInfo => 'Device ID';

  @override
  String get logoutButton => 'Logout';

  @override
  String get personalInfoSaved => 'Personal information saved';

  @override
  String get saveFailed => 'Save failed, please try again';

  @override
  String get notSet => 'Not set';

  @override
  String get editUserInfo => 'Edit User Info';

  @override
  String get confirmLogout => 'Confirm Logout';

  @override
  String get logoutConfirmation => 'Are you sure you want to logout?';

  @override
  String get logoutWithVpnWarning =>
      'Are you sure you want to logout?\n\nNote: Current WAN connection will be automatically disconnected.';

  @override
  String get disconnectAndExit => 'Disconnect and Exit';

  @override
  String get clearLogs => 'Clear Logs';

  @override
  String get confirmClearLogs => 'Are you sure you want to clear all logs?';

  @override
  String get confirm => 'Confirm';

  @override
  String get confirmReconnection => 'Confirm Reconnection';

  @override
  String get routingChangeRequiresReconnection =>
      'Current VPN is connected. Changing routing settings requires reconnection. Do you want to confirm and reconnect?';

  @override
  String get confirmAndReconnect => 'Confirm and Reconnect';

  @override
  String get routingSettingsAppliedAndReconnected =>
      'Routing settings saved and reconnected';

  @override
  String get routingSettingsAppliedDisconnected =>
      'Routing settings saved, VPN disconnected';

  @override
  String get routingSettingsReconnectionFailed =>
      'Routing settings reconnection failed';

  @override
  String get logsExportedTo => 'Logs exported to';

  @override
  String get exportLogsFailed => 'Failed to export logs';

  @override
  String get logCopiedToClipboard => 'Log copied to clipboard';

  @override
  String get allLevels => 'All Levels';

  @override
  String get searchLogs => 'Search logs...';

  @override
  String get logsTitle => 'Logs';

  @override
  String get closeSearch => 'Close Search';

  @override
  String get searchLogsTooltip => 'Search Logs';

  @override
  String get filterByLevel => 'Filter by Level';

  @override
  String get moreActions => 'More Actions';

  @override
  String get exportLogs => 'Export Logs';

  @override
  String get filterPrefix => 'Filter: ';

  @override
  String get clearFilter => 'Clear Filter';

  @override
  String get noLogs => 'No logs';

  @override
  String get noData => 'No data';

  @override
  String get scrollToLatestLog => 'Scroll to Latest Log';

  @override
  String get connectionManagement => 'Connection Management';

  @override
  String get userInfo => 'User Information';

  @override
  String get trafficStatistics => 'Traffic Statistics';

  @override
  String get systemLogs => 'System Logs';

  @override
  String get aboutApp => 'About App';

  @override
  String get connection => 'Connection';

  @override
  String get user => 'User';

  @override
  String get statisticsNav => 'Statistics';

  @override
  String get settingsNav => 'Settings';

  @override
  String get logsNav => 'Logs';

  @override
  String get aboutNav => 'About';

  @override
  String get auto => 'Auto';

  @override
  String get statusUpdated => 'Status updated';

  @override
  String get routingMode => 'Routing Mode';

  @override
  String get allRouting => 'All Routing';

  @override
  String get allRoutingDescription => 'All traffic goes through WAN tunnel';

  @override
  String get customRouting => 'Custom Routing';

  @override
  String get customRoutingDescription =>
      'Only specified network segments go through WAN tunnel';

  @override
  String get enterNetworkSegments => 'Please enter network segments to route';

  @override
  String get networkSegmentsExample =>
      'Separate multiple segments with commas, e.g.: ***********/16,10.0.0.0/8';

  @override
  String get enterNetworkSegmentsHint => 'Enter network segments...';

  @override
  String get ensureCorrectCidrFormat => 'Please ensure correct CIDR format';

  @override
  String get uploadSpeed => 'Upload';

  @override
  String get downloadSpeed => 'Download';

  @override
  String get unreachable => 'Unreachable';

  @override
  String get excellent => 'Excellent';

  @override
  String get good => 'Good';

  @override
  String get poor => 'Poor';

  @override
  String get languageSettings => 'Language Settings';

  @override
  String get selectLanguage => 'Select Language';

  @override
  String get chinese => '中文';

  @override
  String get english => 'English';

  @override
  String get languageChanged => 'Language changed';

  @override
  String get pleaseSelectServer => 'Please select a server first';

  @override
  String get connectingToServer => 'Connecting...';

  @override
  String get disconnectingFromServer => 'Disconnecting...';

  @override
  String get connectionTimeoutDetailed =>
      'Connection timeout, please check network connection or try again later';

  @override
  String get connectionFailedGeneric => 'Connection failed';

  @override
  String get disconnectedFromServer => 'Disconnected';

  @override
  String switchingToServer(Object serverName) {
    return 'Switching to $serverName...';
  }

  @override
  String connectedToServer(Object serverName) {
    return 'Connected to $serverName';
  }

  @override
  String currentlyConnectedTo(Object newServerName, Object serverName) {
    return 'Currently connected to $serverName, switching to $newServerName';
  }

  @override
  String get selectServerFirst => 'Please select a server first';

  @override
  String get operationTimeout => 'Operation timeout, please try again later';

  @override
  String pingServersFailed(Object error) {
    return 'Ping servers failed: $error';
  }

  @override
  String get networkConnectionFailed =>
      'Network connection failed, please check network settings';

  @override
  String get realtimeConnectionInterrupted =>
      'Real-time connection interrupted, attempting to reconnect...';

  @override
  String get authenticationFailed =>
      'Authentication failed, please login again';

  @override
  String get operationFailedRetry => 'Operation failed, please try again later';

  @override
  String get systemTrayConnected => 'Connected';

  @override
  String get systemTrayConnecting => 'Connecting...';

  @override
  String get systemTrayDisconnecting => 'Disconnecting...';

  @override
  String get systemTrayDisconnected => 'Disconnected';

  @override
  String get showWindow => 'Show Window';

  @override
  String get hideWindow => 'Hide Window';

  @override
  String get exitApp => 'Exit App';

  @override
  String get processing => 'Processing...';

  @override
  String get calculating => 'Calculating...';

  @override
  String get operationCancelled => 'Operation cancelled';

  @override
  String get testLatency => 'Test Latency';

  @override
  String get testingLatency => 'Testing latency...';

  @override
  String get latencyTestComplete => 'Latency test complete';

  @override
  String currentlyConnectedToServer(Object serverName) {
    return 'Currently connected to: $serverName';
  }

  @override
  String get unknownServer => 'Unknown server';

  @override
  String get noAutoServersAvailable =>
      'No auto servers available, please check network connection or contact administrator';

  @override
  String get autoServerSelectionFailed =>
      'Auto server selection failed, please select server manually or try again later';

  @override
  String get apiInvalidRequest => 'Invalid request format or parameters';

  @override
  String get apiInvalidCredentials => 'Invalid username or password';

  @override
  String get apiServerError => 'Server internal error';

  @override
  String get apiResourceNotFound => 'Requested resource not found';

  @override
  String get apiUnauthorized => 'Unauthorized access, please login again';

  @override
  String get apiForbidden => 'Access to this resource is forbidden';

  @override
  String get apiTimeout => 'Request timeout, please try again later';

  @override
  String get apiConflict => 'Resource conflict';

  @override
  String get apiRateLimit => 'Too many requests, please try again later';

  @override
  String get apiGatewayError => 'Gateway error';

  @override
  String get apiServiceUnavailable =>
      'Service temporarily unavailable, please try again later';

  @override
  String get networkUnreachable =>
      'Network unreachable, please check network connection';

  @override
  String get networkDnsFailure =>
      'DNS resolution failed, please check server address';

  @override
  String get networkConnectionReset => 'Network connection was reset';

  @override
  String get networkConnectionClosed => 'Network connection was closed';

  @override
  String get networkProxyError => 'Proxy server error';

  @override
  String get networkTlsError => 'TLS/SSL error';

  @override
  String get authInvalidCredentials => 'Invalid user credentials';

  @override
  String get authExpiredCredentials =>
      'Credentials expired, please login again';

  @override
  String get authRateLimit =>
      'Authentication requests too frequent, please try again later';

  @override
  String get authAccountLocked =>
      'Account locked, please contact administrator';

  @override
  String get authInvalidToken => 'Invalid authentication token';

  @override
  String get authTokenExpired =>
      'Authentication token expired, please login again';

  @override
  String get authMissingCredentials => 'Missing authentication credentials';

  @override
  String get tunnelError => 'Tunnel error';

  @override
  String get tunnelInitFailed => 'Tunnel initialization failed';

  @override
  String get tunnelCloseFailed => 'Tunnel close failed';

  @override
  String get tunnelReadFailed => 'Tunnel read failed';

  @override
  String get tunnelWriteFailed => 'Tunnel write failed';

  @override
  String get tunnelConfigFailed => 'Tunnel configuration failed';

  @override
  String get configError => 'Configuration error';

  @override
  String get configInvalid => 'Invalid configuration';

  @override
  String get configFileNotFound => 'Configuration file not found';

  @override
  String get configFileReadFailed => 'Configuration file read failed';

  @override
  String get configFileWriteFailed => 'Configuration file write failed';

  @override
  String get configFileParseFailed => 'Configuration file parse failed';

  @override
  String get platformError => 'Platform error';

  @override
  String get platformUnsupported => 'Unsupported platform';

  @override
  String get platformInitFailed => 'Platform initialization failed';

  @override
  String get platformIoError => 'Platform IO error';

  @override
  String get platformPermissionDenied =>
      'Permission denied, please run as administrator';

  @override
  String get domainLookupFailed => 'Domain lookup failed';

  @override
  String get domainNotFound => 'Domain not found, please check the domain name';

  @override
  String get domainInvalid => 'Invalid domain format';

  @override
  String get domainRequired => 'Domain parameter is required';

  @override
  String get domainLookupTimeout =>
      'Domain lookup timeout, please try again later';

  @override
  String get domainLookupNetworkError =>
      'Network error during domain lookup, please check your connection';

  @override
  String get serverListNotFound =>
      'Server list not found, please check the server URL';

  @override
  String get serverListInvalid => 'Invalid server list format';

  @override
  String get serverListTimeout =>
      'Server list request timeout, please try again later';

  @override
  String get serverListNetworkError =>
      'Network error while fetching server list, please check your connection';

  @override
  String get protocolError => 'Protocol error';

  @override
  String get protocolInvalid => 'Invalid protocol';

  @override
  String get protocolUnsupported => 'Unsupported protocol';

  @override
  String get protocolVersionMismatch => 'Protocol version mismatch';

  @override
  String get protocolHandshakeFailed => 'Protocol handshake failed';

  @override
  String get protocolEncryptionFailed => 'Protocol encryption failed';

  @override
  String get protocolDecryptionFailed => 'Protocol decryption failed';

  @override
  String get unknownError => 'Unknown error';

  @override
  String get invalidParameter => 'Invalid parameter';

  @override
  String get notImplemented => 'Feature not implemented';

  @override
  String get permissionDenied => 'Permission denied';

  @override
  String get checkUpdate => 'Check Update';

  @override
  String get checkingUpdate => 'Checking for updates...';

  @override
  String get updateAvailable => 'Update Available';

  @override
  String get updateNotAvailable => 'No updates available';

  @override
  String get currentVersion => 'Current Version';

  @override
  String get latestVersion => 'Latest Version';

  @override
  String get updateNow => 'Update Now';

  @override
  String get updateLater => 'Remind Later';

  @override
  String get skipUpdate => 'Skip Update';

  @override
  String get downloading => 'Downloading...';

  @override
  String get downloadProgress => 'Download Progress';

  @override
  String get downloadComplete => 'Download Complete';

  @override
  String get downloadFailed => 'Download Failed';

  @override
  String get installing => 'Installing...';

  @override
  String get installComplete => 'Installation Complete';

  @override
  String get installFailed => 'Installation Failed';

  @override
  String get installStarted =>
      'Installation started. Please tap \'Install\' in the system dialog to complete the app update. Please restart the app manually after installation.';

  @override
  String get restartAppMessage =>
      'Update installation complete. Please restart the app to use the new version.';

  @override
  String get updateFailed => 'Update Failed';

  @override
  String get updateCancelled => 'Update Cancelled';

  @override
  String get forceUpdate => 'Force Update';

  @override
  String get forceUpdateMessage =>
      'This update contains critical fixes and must be installed immediately';

  @override
  String get releaseNotes => 'Release Notes';

  @override
  String get fileValidationFailed => 'File validation failed';

  @override
  String get insufficientStorage => 'Insufficient storage space';

  @override
  String get networkUnavailable => 'Network connection unavailable';

  @override
  String get wifiRequired => 'WiFi connection required';

  @override
  String get permissionRequired => 'Installation permission required';

  @override
  String get versionInfo => 'Version Info';
}
